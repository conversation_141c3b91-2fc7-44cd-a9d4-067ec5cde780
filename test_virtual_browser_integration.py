# -*- coding: utf-8 -*-
"""
测试指纹浏览器集成功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from crawlers.spiders.douyin.douyin_api import DouyinAPI


async def test_virtual_browser_integration():
    """测试指纹浏览器集成"""
    print("=" * 50)
    print("测试指纹浏览器集成功能")
    print("=" * 50)

    api = DouyinAPI()

    try:
        # 1. 测试获取指纹浏览器列表
        print("\n1. 测试获取指纹浏览器列表...")
        browser_list_result = api.get_virtual_browser_list()
        print(f"结果: {browser_list_result}")

        # 2. 测试初始化API（使用指纹浏览器）
        print("\n2. 测试初始化API（使用指纹浏览器）...")
        try:
            init_result = await api.initialize(
                need_login=False,
                headless=False,
                enable_proxy=False,
                use_virtual_browser=True
            )
            print(f"初始化结果: {init_result}")
        except Exception as e:
            print(f"指纹浏览器初始化失败（预期的）: {str(e)}")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 5. 测试关闭API
        print("\n5. 测试关闭API...")
        close_result = await api.close()
        print(f"关闭结果: {close_result}")

async def test_normal_browser():
    """测试普通浏览器模式（对比）"""
    print("\n" + "=" * 50)
    print("测试普通浏览器模式（对比）")
    print("=" * 50)

    api = DouyinAPI()

    try:
        # 测试初始化API（使用普通浏览器）
        print("\n1. 测试初始化API（使用普通浏览器）...")
        init_result = await api.initialize(
            need_login=False,
            headless=True,
            enable_proxy=False,
            use_virtual_browser=False  # 使用普通浏览器
        )
        print(f"初始化结果: {init_result}")

        if init_result["success"]:
            # 测试API状态
            print("\n2. 测试API状态...")
            status = api.get_status()
            print(f"API状态: {status}")

        else:
            print(f"初始化失败: {init_result['message']}")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭API
        print("\n3. 测试关闭API...")
        close_result = await api.close()
        print(f"关闭结果: {close_result}")


async def main():
    """主测试函数"""
    print("开始测试抖音API指纹浏览器集成功能")

    # 测试指纹浏览器集成
    await test_virtual_browser_integration()

    # 等待一下
    await asyncio.sleep(2)
    print("\n所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())

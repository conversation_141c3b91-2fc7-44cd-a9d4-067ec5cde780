import asyncio
import os
import random
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>

from playwright.async_api import Browser<PERSON><PERSON>x<PERSON>, BrowserType, Page, async_playwright

import config
from crawlers.spiders.xhs.client import <PERSON>HongShuClient
from crawlers.spiders.xhs.exception import DataFetchError
from crawlers.spiders.xhs.field import SearchSortType, SearchNoteType
from crawlers.spiders.xhs.help import get_search_id
from crawlers.spiders.xhs.login import XiaoHongShuLogin
from crawlers.proxy.proxy_ip_pool import IpInfoModel
from tools import utils


class XiaoHongShuSearch:
    """
    小红书搜索类，用于实现小红书搜索功能，支持自定义关键词、搜索数量限制和排序规则
    """
    context_page: Page
    xhs_client: XiaoHongShuClient
    browser_context: BrowserContext

    def __init__(self, headless: bool = True,
                 use_local_chrome: bool = False,
                 chrome_path: str = None,
                 user_data_dir: str = None) -> None:

        self.index_url = "https://www.xiaohongshu.com"
        self.user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        self.headless = headless
        self.use_local_chrome = use_local_chrome
        self.chrome_path = chrome_path
        self.user_data_dir = user_data_dir
        self.initialized = False
        self.playwright = None

    async def initialize(self) -> None:
        """
        初始化浏览器和客户端
        """
        if self.initialized:
            return

        self.playwright = await async_playwright().start()

        chromium = self.playwright.chromium
        self.browser_context = await self.launch_browser(
            chromium, self.user_agent, headless=self.headless
        )

        await self.browser_context.add_init_script(path="libs/stealth.min.js")
        # add a cookie attribute webId to avoid the appearance of a sliding captcha on the webpage
        await self.browser_context.add_cookies(
            [
                {
                    "name": "webId",
                    "value": "xxx123",  # any value
                    "domain": ".xiaohongshu.com",
                    "path": "/",
                }
            ]
        )
        self.context_page = await self.browser_context.new_page()
        await self.context_page.goto(self.index_url)

        httpx_proxy_format = None
        self.xhs_client = await self.create_xhs_client(httpx_proxy_format)
        if not await self.xhs_client.pong():
            login_obj = XiaoHongShuLogin(
                login_type="qrcode",
                login_phone="",
                browser_context=self.browser_context,
                context_page=self.context_page,
                cookie_str=config.COOKIES,
            )
            await login_obj.begin()
            await self.xhs_client.update_cookies(
                browser_context=self.browser_context
            )

        self.initialized = True
        utils.logger.info("[XiaoHongShuSearch.initialize] Initialization completed")

    async def search(
            self,
            keyword: str,
            limit: int = 300,
            sort_type: SearchSortType = SearchSortType.LATEST,
            note_type: SearchNoteType = SearchNoteType.ALL
    ) -> List[Dict]:
        """
        搜索小红书笔记

        Args:
            keyword: 关键词，可以是字符串（用逗号分隔多个关键词）或关键词列表
            limit: 每个关键词最多获取的结果数量，默认为100
            sort_type: 排序类型，默认为最新发布
            note_type: 笔记类型，默认为全部
            save_to_file: 是否将结果保存到文件，默认为False
            output_file: 输出文件名，默认为search_results.json

        Returns:
            Dict: 搜索结果，格式为 {关键词: [页面结果列表]}
        """
        # 确保已初始化
        if not self.initialized:
            await self.initialize()

        if not keyword:
            raise ValueError("No valid keyword provided")

        utils.logger.info(
            f"[XiaoHongShuSearch.search] Begin search xiaohongshu keyword: {keyword}"
        )

        # 确保limit至少为20（小红书API的最小页面大小）
        xhs_limit_count = 20
        if limit < xhs_limit_count:
            limit = xhs_limit_count

        utils.logger.info(
            f"[XiaoHongShuSearch.search] Current search keyword: {keyword}"
        )
        page = 1
        search_id = get_search_id()
        keyword_results = []

        start_page = 1
        while (page - start_page + 1) * xhs_limit_count <= limit:
            if page < start_page:
                utils.logger.info(f"[XiaoHongShuSearch.search] Skip page {page}")
                page += 1
                continue

            try:
                utils.logger.info(
                    f"[XiaoHongShuSearch.search] search xhs keyword: {keyword}, page: {page}"
                )
                notes_res = await self.xhs_client.get_note_by_keyword(
                    keyword=keyword,
                    search_id=search_id,
                    page=page,
                    page_size=xhs_limit_count,
                    sort=sort_type,
                    note_type=note_type
                )

                # Add results to our collection
                if notes_res and 'items' in notes_res:
                    keyword_results.extend(notes_res['items'])
                    utils.logger.info(
                        f"[XiaoHongShuSearch.search] Found {len(notes_res.get('items', []))} items on page {page}"
                    )

                if not notes_res or not notes_res.get("has_more", False):
                    utils.logger.info("[XiaoHongShuSearch.search] No more content!")
                    break

                page += 1
                await asyncio.sleep(random.uniform(0.5, 1.5))

            except DataFetchError as e:
                utils.logger.error(
                    f"[XiaoHongShuSearch.search] Get note detail error: {e}"
                )
                break

        utils.logger.info(f"[XiaoHongShuSearch.search] Total items for '{keyword}': {len(keyword_results)}")

        return keyword_results

    async def get_note_details(self, search_results: Dict) -> List[Dict]:
        """
        获取搜索结果中笔记的详细信息

        Args:
            search_results: 搜索结果，格式为 {关键词: [页面结果列表]}

        Returns:
            List[Dict]: 笔记详细信息列表
        """
        if not self.initialized:
            await self.initialize()

        note_details = []
        # 最多的并发量
        semaphore = asyncio.Semaphore(1)

        for keyword, results in search_results.items():
            for page_result in results:
                if "items" not in page_result:
                    continue

                task_list = [
                    self.get_note_detail_async_task(
                        note_id=post_item.get("id"),
                        xsec_source=post_item.get("xsec_source"),
                        xsec_token=post_item.get("xsec_token"),
                        semaphore=semaphore,
                    )
                    for post_item in page_result.get("items", {})
                    if post_item.get("model_type") not in ("rec_query", "hot_query")
                ]

                details = await asyncio.gather(*task_list)
                for detail in details:
                    if detail:
                        note_details.append(detail)

        utils.logger.info(f"[XiaoHongShuSearch.get_note_details] Retrieved {len(note_details)} note details")
        return note_details

    async def get_note_detail(self, note_id: str, xsec_source: str, xsec_token: str):
        semaphore = asyncio.Semaphore(1)

        note_details = await self.get_note_detail_async_task(
            note_id=note_id,
            xsec_source=xsec_source,
            xsec_token=xsec_token,
            semaphore=semaphore
        )
        return note_details

    async def get_note_detail_async_task(
            self,
            note_id: str,
            xsec_source: str,
            xsec_token: str,
            semaphore: asyncio.Semaphore,
    ) -> Optional[Dict]:
        """获取笔记详情

        Args:
            note_id: 笔记ID
            xsec_source: 来源
            xsec_token: 验证token
            semaphore: 信号量，用于控制并发

        Returns:
            Dict: 笔记详情
        """
        note_detail_from_html, note_detail_from_api = None, None
        async with semaphore:
            # 控制爬取间隔 2~4秒
            crawl_interval = random.uniform(2, 4)
            try:
                # 尝试直接获取网页版笔记详情，携带cookie
                note_detail_from_html: Optional[Dict] = (
                    await self.xhs_client.get_note_by_id_from_html(
                        note_id, xsec_source, xsec_token, enable_cookie=True
                    )
                )
                await asyncio.sleep(crawl_interval)
                if not note_detail_from_html:
                    # 如果网页版笔记详情获取失败，则尝试不使用cookie获取
                    note_detail_from_html = (
                        await self.xhs_client.get_note_by_id_from_html(
                            note_id, xsec_source, xsec_token, enable_cookie=False
                        )
                    )
                    utils.logger.error(
                        f"[XiaoHongShuSearch.get_note_detail_async_task] Get note detail error, note_id: {note_id}"
                    )
                if not note_detail_from_html:
                    # 如果网页版笔记详情获取失败，则尝试API获取
                    note_detail_from_api: Optional[Dict] = (
                        await self.xhs_client.get_note_by_id(
                            note_id, xsec_source, xsec_token
                        )
                    )
                note_detail = note_detail_from_html or note_detail_from_api
                if note_detail:
                    note_detail.update(
                        {"xsec_token": xsec_token, "xsec_source": xsec_source}
                    )
                    return note_detail
            except DataFetchError as ex:
                utils.logger.error(
                    f"[XiaoHongShuSearch.get_note_detail_async_task] Get note detail error: {ex}"
                )
                return None
            except KeyError as ex:
                utils.logger.error(
                    f"[XiaoHongShuSearch.get_note_detail_async_task] have not fund note detail note_id:{note_id}, err: {ex}"
                )
                return None

    @staticmethod
    def format_proxy_info(
            ip_proxy_info: IpInfoModel, ) -> Tuple[Optional[Dict], Optional[Dict]]:
        """格式化代理信息"""
        playwright_proxy = {
            "server": f"{ip_proxy_info.protocol}{ip_proxy_info.ip}:{ip_proxy_info.port}",
            "username": ip_proxy_info.user,
            "password": ip_proxy_info.password,
        }
        httpx_proxy = {
            f"{ip_proxy_info.protocol}": f"http://{ip_proxy_info.user}:{ip_proxy_info.password}@{ip_proxy_info.ip}:{ip_proxy_info.port}"
        }
        return playwright_proxy, httpx_proxy

    async def create_xhs_client(self, httpx_proxy: Optional[str]) -> XiaoHongShuClient:
        """创建小红书客户端"""
        utils.logger.info(
            "[XiaoHongShuSearch.create_xhs_client] Begin create xiaohongshu API client ..."
        )
        cookie_str, cookie_dict = utils.convert_cookies(
            await self.browser_context.cookies()
        )
        xhs_client_obj = XiaoHongShuClient(
            proxies=httpx_proxy,
            headers={
                "User-Agent": self.user_agent,
                "Cookie": cookie_str,
                "Origin": "https://www.xiaohongshu.com",
                "Referer": "https://www.xiaohongshu.com",
                "Content-Type": "application/json;charset=UTF-8",
            },
            playwright_page=self.context_page,
            cookie_dict=cookie_dict,
        )
        return xhs_client_obj

    async def launch_browser(
            self,
            chromium: BrowserType,
            user_agent: Optional[str],
            headless: bool = True
    ) -> BrowserContext:
        """启动浏览器并创建浏览器上下文"""
        utils.logger.info(
            "[XiaoHongShuSearch.launch_browser] Begin create browser context ..."
        )

        # 使用本地Chrome浏览器
        if self.use_local_chrome:
            utils.logger.info("[XiaoHongShuSearch.launch_browser] Using local Chrome browser")

            # 设置Chrome可执行文件路径
            executable_path = self.chrome_path

            # 设置用户数据目录
            user_data_dir = self.user_data_dir
            if not user_data_dir:
                # 如果未指定用户数据目录，使用默认目录
                user_data_dir = os.path.join(
                    os.getcwd(), "browser_data", config.USER_DATA_DIR % config.PLATFORM
                )

            utils.logger.info(f"[XiaoHongShuSearch.launch_browser] Chrome path: {executable_path}")
            utils.logger.info(f"[XiaoHongShuSearch.launch_browser] User data directory: {user_data_dir}")

            # 启动Chrome浏览器
            browser_context = await chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                executable_path=executable_path if executable_path else None,
                accept_downloads=True,
                headless=headless,
                viewport={"width": 1920, "height": 1080},
                user_agent=user_agent,
            )
            return browser_context

        # 使用内置浏览器
        elif config.SAVE_LOGIN_STATE:
            # 保存登录状态，避免每次都需要登录
            user_data_dir = os.path.join(
                os.getcwd(), "browser_data", config.USER_DATA_DIR % config.PLATFORM
            )  # type: ignore
            browser_context = await chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                accept_downloads=True,
                headless=headless,
                viewport={"width": 1920, "height": 1080},
                user_agent=user_agent,
            )
            return browser_context
        else:
            browser = await chromium.launch(headless=headless)  # type: ignore
            browser_context = await browser.new_context(
                viewport={"width": 1920, "height": 1080}, user_agent=user_agent
            )
            return browser_context

    async def close(self):
        """关闭浏览器上下文"""
        if hasattr(self, 'browser_context'):
            await self.browser_context.close()
            utils.logger.info("[XiaoHongShuSearch.close] Browser context closed ...")

        if hasattr(self, 'playwright'):
            await self.playwright.stop()
            utils.logger.info("[XiaoHongShuSearch.close] Playwright stopped ...")

        self.initialized = False

    async def query_user_data_list(self, user_id_list: List[str], ) -> Dict:

        """批量查询用户信息"""
        if not self.initialized:
            raise Exception("XiaoHongShuSearch not initialized")
        if not user_id_list or len(user_id_list) == 0:
            return {}
        user_profile_dict = {}
        for user_id in user_id_list:
            if not user_id:
                utils.logger.error("[query_user_profile]: home url is empty")
                continue
            user_profile_dict[user_id] = await self._query_user_data(user_id)
        return user_profile_dict

    async def _query_user_data(self, user_id: str) -> Dict:
        """查询用户信息"""
        try:
            user_info = await self.xhs_client.get_notes_by_creator(creator=user_id, cursor="")
            return user_info
        except Exception as e:
            utils.logger.error(f"[_query_user_profile]: {e}")
            return {}

    async def query_user_profile(self, user_id: str) -> Dict:
        """查询用户信息"""
        if not self.initialized:
            raise Exception("XiaoHongShuSearch not initialized")
        if not user_id:
            utils.logger.error("[query_user_profile]: home url is empty")
            return {}
        user_info = await self.xhs_client.get_creator_info(user_id=user_id)
        return user_info


# 示例用法
async def main():
    searcher = XiaoHongShuSearch(
        headless=False,
        use_local_chrome=True,
        user_data_dir=r"F:\account\xhs\yufeng",
        chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe")

    try:
        # 初始化
        await searcher.initialize()

        user_info_list = await searcher.query_user_data_list(user_id_list=["5c45ea120000000012002e1e"])
        print(user_info_list)
    except Exception as e:
        utils.logger.error(f"main: {e}")

    finally:
        # 关闭浏览器
        await searcher.close()


if __name__ == "__main__":
    asyncio.run(main())

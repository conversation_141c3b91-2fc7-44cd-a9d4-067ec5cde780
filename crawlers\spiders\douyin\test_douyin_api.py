#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试抖音API接口
"""
import asyncio
import json
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawlers.spiders.douyin.douyin_api import DouyinAPI


async def test_douyin_api():
    """测试抖音API的各项功能"""
    print("=" * 60)
    print("抖音API测试")
    print("=" * 60)
    
    api = DouyinAPI()
    
    try:
        # 1. 测试初始化（不登录模式）
        print("\n1. 测试初始化（不登录模式）...")
        init_result = await api.initialize(
            need_login=False,
            headless=True,  # 无头模式，避免弹出浏览器
            enable_proxy=False
        )
        print(f"初始化结果: {json.dumps(init_result, ensure_ascii=False, indent=2)}")
        
        if not init_result["success"]:
            print("初始化失败，退出测试")
            return
        
        # 2. 测试获取状态
        print("\n2. 测试获取API状态...")
        status = api.get_status()
        print(f"API状态: {json.dumps(status, ensure_ascii=False, indent=2)}")
        
        # 3. 测试搜索视频
        print("\n3. 测试搜索视频...")
        search_result = await api.search_videos(
            keyword="编程",
            count=5,
            sort_type=0,  # 综合排序
            publish_time=0  # 不限时间
        )
        print(f"搜索结果: {json.dumps(search_result, ensure_ascii=False, indent=2)}")
        
        # 如果搜索成功，继续测试其他功能
        if search_result["success"] and search_result["data"] and search_result["data"]["videos"]:
            first_video = search_result["data"]["videos"][0]
            aweme_id = first_video["aweme_id"]
            author_sec_uid = first_video["author"]["sec_uid"]
            
            print(f"\n使用第一个视频进行后续测试:")
            print(f"视频ID: {aweme_id}")
            print(f"视频描述: {first_video['desc']}")
            print(f"作者: {first_video['author']['nickname']}")
            
            # 4. 测试获取视频详情
            print("\n4. 测试获取视频详情...")
            detail_result = await api.get_video_detail(aweme_id)
            if detail_result["success"]:
                print("视频详情获取成功")
                print(f"详情数据键: {list(detail_result['data'].keys()) if detail_result['data'] else '无数据'}")
            else:
                print(f"视频详情获取失败: {detail_result['message']}")
            
            # 5. 测试获取视频评论
            print("\n5. 测试获取视频评论...")
            comments_result = await api.get_video_comments(
                aweme_id=aweme_id,
                count=5,
                get_sub_comments=False  # 暂时不获取子评论，避免过多请求
            )
            if comments_result["success"]:
                comments_count = len(comments_result["data"]["comments"])
                print(f"成功获取 {comments_count} 条评论")
                if comments_count > 0:
                    first_comment = comments_result["data"]["comments"][0]
                    print(f"第一条评论: {first_comment['text'][:50]}...")
            else:
                print(f"评论获取失败: {comments_result['message']}")
            
            # 6. 测试获取用户信息
            if author_sec_uid:
                print("\n6. 测试获取用户信息...")
                user_info_result = await api.get_user_info(author_sec_uid)
                if user_info_result["success"]:
                    user_data = user_info_result["data"]
                    print(f"用户昵称: {user_data['nickname']}")
                    print(f"粉丝数: {user_data['follower_count']}")
                    print(f"作品数: {user_data['aweme_count']}")
                else:
                    print(f"用户信息获取失败: {user_info_result['message']}")
                
                # 7. 测试获取用户作品
                print("\n7. 测试获取用户作品...")
                user_videos_result = await api.get_user_videos(
                    sec_user_id=author_sec_uid,
                    count=3
                )
                if user_videos_result["success"]:
                    videos_count = len(user_videos_result["data"]["videos"])
                    print(f"成功获取 {videos_count} 个用户作品")
                    for i, video in enumerate(user_videos_result["data"]["videos"][:2]):
                        print(f"作品{i+1}: {video['desc'][:30]}...")
                else:
                    print(f"用户作品获取失败: {user_videos_result['message']}")
            else:
                print("\n6-7. 跳过用户相关测试（无有效的sec_uid）")
        
        else:
            print("搜索无结果，跳过后续测试")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 8. 测试关闭API
        print("\n8. 关闭API...")
        close_result = await api.close()
        print(f"关闭结果: {json.dumps(close_result, ensure_ascii=False, indent=2)}")


async def test_login_mode():
    """测试登录模式（需要手动操作）"""
    print("\n" + "=" * 60)
    print("测试登录模式（需要手动扫码）")
    print("=" * 60)
    
    api = DouyinAPI()
    
    try:
        # 测试登录模式初始化
        init_result = await api.initialize(
            need_login=True,
            login_type="qrcode",  # 二维码登录
            headless=False,  # 显示浏览器，方便扫码
            enable_proxy=False
        )
        print(f"登录模式初始化结果: {json.dumps(init_result, ensure_ascii=False, indent=2)}")
        
        if init_result["success"] and init_result["logged_in"]:
            print("登录成功！可以进行需要登录的操作...")
            
            # 这里可以添加需要登录才能执行的操作
            status = api.get_status()
            print(f"登录后状态: {json.dumps(status, ensure_ascii=False, indent=2)}")
        
    except Exception as e:
        print(f"登录测试过程中发生错误: {e}")
    
    finally:
        close_result = await api.close()
        print(f"关闭结果: {json.dumps(close_result, ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    # 运行基础测试
    asyncio.run(test_douyin_api())
    
    # 如果需要测试登录模式，取消下面的注释
    # print("\n是否测试登录模式？(y/n): ", end="")
    # if input().lower() == 'y':
    #     asyncio.run(test_login_mode())

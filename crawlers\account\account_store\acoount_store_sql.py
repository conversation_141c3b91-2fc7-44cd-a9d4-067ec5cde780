from typing import Dict, List

from repositories.async_db import AsyncMysqlDB
from common.var import media_crawler_db_var


async def query_content_by_content_id(agent_uuid: str, platform: str) -> Dict:
    """
     查询可用的账号
    """
    async_db_conn: AsyncMysqlDB = media_crawler_db_var.get()
    sql: str = f"select * from account_platform where agent_uuid = '{agent_uuid}' where status = 1 and is_del = 0"
    rows: List[Dict] = await async_db_conn.query(sql)
    if len(rows) > 0:
        return rows[0]
    return dict()


async def insert_new_account(account_model: Dict):
    """
     插入新的账户
    """
    async_db_conn: AsyncMysqlDB = media_crawler_db_var.get()
    last_row_id: int = await async_db_conn.item_to_table("account_platform", account_model)
    return last_row_id


async def update_account_status(account_model: Dict):
    """
     插入新的账户
    """
    async_db_conn: AsyncMysqlDB = media_crawler_db_var.get()
    last_row_id: int = await async_db_conn.item_to_table("account_platform", account_model)
    return last_row_id


async def del_account_status(account_uuid: str, agent_uuid: str):
    """
     删除账户
    Args:
        account_uuid:
        agent_uuid:

    Returns:

    """
    async_db_conn: AsyncMysqlDB = media_crawler_db_var.get()
    sql = f"update account set is_del = 1 where agent_uuid = {agent_uuid} and account_uuid = {account_uuid}"
    last_row_id: int = await async_db_conn.execute(sql)
    return last_row_id

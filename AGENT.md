# Agent Configuration for Media Crawler

## Commands
- **Run main**: `python main.py`
- **Type check**: `mypy .` (config in mypy.ini)
- **Run single test**: `python -m unittest test.test_utils.test_convert_cookies`
- **Run all tests**: `python -m unittest discover test/`
- **Install deps**: `uv sync` or `pip install -r requirements.txt`
- **Docs dev**: `npm run docs:dev`

## Code Style
- Use `# -*- coding: utf-8 -*-` header in all Python files
- Type annotations required (mypy enabled)
- Abstract base classes for crawler interfaces
- Async/await for I/O operations (asyncio, aiofi<PERSON>, playwright)
- CamelCase for classes (e.g., `XiaoHongShuCrawler`, `AbstractCrawler`)
- snake_case for functions and variables
- Factory pattern for crawler creation
- Import order: standard library, third-party, local modules

## Architecture
- Platform-specific crawlers inherit from `AbstractCrawler`
- Config-driven platform selection
- Playwright for browser automation
- Support for proxy rotation and fingerprint browsers

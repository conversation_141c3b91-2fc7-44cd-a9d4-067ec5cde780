# -*- coding: utf-8 -*-

import os

# mysql config
RELATION_DB_PWD = os.getenv("RELATION_DB_PWD", "@test-data-plus@^^123")
RELATION_DB_USER = os.getenv("RELATION_DB_USER", "data")
RELATION_DB_HOST = os.getenv("RELATION_DB_HOST", "rm-f8zc8983uf3z2vpk7zo.mysql.rds.aliyuncs.com")
RELATION_DB_PORT = os.getenv("RELATION_DB_PORT", 3306)
RELATION_DB_NAME = os.getenv("RELATION_DB_NAME", "test_core")
RELATION_DB_CONN_LIMIT_MAX = os.getenv("RELATION_DB_CONN_LIMIT_MAX", 10)
RELATION_DB_CONN_LIMIT_MIN = os.getenv("RELATION_DB_CONN_LIMIT", 1)


# redis config
REDIS_DB_HOST = "r-f8zhjcd0pr7otfwzw4pd.redis.rds.aliyuncs.com"  # your redis host
REDIS_DB_PWD = os.getenv("REDIS_DB_PWD", "@test-data-plus@^*123")  # your redis password
REDIS_DB_PORT = os.getenv("REDIS_DB_PORT", 6379)  # your redis port
REDIS_DB_NUM = os.getenv("REDIS_DB_NUM", 5)  # your redis db num

# cache type
CACHE_TYPE_REDIS = "redis"
CACHE_TYPE_MEMORY = "memory"
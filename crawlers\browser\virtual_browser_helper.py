# -*- coding: utf-8 -*-
# @Time : 2025/5/28 17:00
# <AUTHOR> cyf
# @File : virtual_browser_helper.py

import asyncio
from typing import List, Optional

from common.crawler_platform import CrawlerPlatform
from crawlers.browser.virtual_browser_sdk import VirtualBrowserSDK


class VirtualBrowserHelper:

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.browser_api = VirtualBrowserSDK("http://localhost:9000", api_key)

    def set_up_group(self, group_name_list: List[CrawlerPlatform]) -> bool:
        """
        初始化各爬虫分组分组，第一次启动 浏览器分组
        """
        try:
            # 获取现有分组列表
            existing_groups = self.browser_api.get_group_list()
            existing_group_names = {group.get("name") for group in existing_groups}

            # 遍历需要创建的分组
            for platform in group_name_list:
                group_name = self._cover_name(platform)

                # 如果分组不存在，则创建
                if group_name not in existing_group_names:
                    success = self.browser_api.add_group(group_name)
                    if not success:
                        return False

            return True
        except Exception as e:
            print(f"设置分组失败: {e}")
            return False

    async def apply_browser(
        self, launch_new: bool, crawler_group: CrawlerPlatform = None
    ) -> Optional[str]:
        """
        launch_new = Ture 则直接在分组下添加并启动一个新的浏览器，

        launch_new = False 如果该平台分组下有未启动的浏览器，则选择一个启动，
        如果没有则增加一个浏览器实例， 同时启动，返回 debuggerPort
        :param launch_new:
        :param crawler_group:
        :return:
        """
        try:
            group_name = self._cover_name(crawler_group)

            if launch_new:
                # 直接创建新浏览器并启动
                browser_id = self._create_new_browser(group_name)
                if browser_id:
                    return await self._launch_and_get_port(browser_id)
            else:
                # 查找该分组下是否有未启动的浏览器
                available_browser = self._find_available_browser(group_name)

                if available_browser:
                    # 启动现有的未启动浏览器
                    return await self._launch_and_get_port(available_browser["id"])
                else:
                    # 没有可用浏览器，创建新的并启动
                    browser_id = self._create_new_browser(group_name)
                    if browser_id:
                        return await self._launch_and_get_port(browser_id)

            return None
        except Exception as e:
            print(f"申请浏览器失败: {e}")
            return None

    async def destroy_browser(self, browser_id: int) -> Optional[bool]:
        """停止并删除浏览器实例"""
        try:
            await self.browser_api.stop_browser(browser_id)
            return self.browser_api.del_browser(browser_id)
        except Exception as e:
            print(f"销毁浏览器失败: {e}")
            return False

    def get_browser_list(self, group_name: str = None) -> List[dict]:
        """获取浏览器列表

        Args:
            group_name: 分组名称，如果不提供则返回所有浏览器

        Returns:
            List[dict]: 浏览器列表
        """
        try:
            return self.browser_api.get_browser_list(group=group_name)
        except Exception as e:
            print(f"获取浏览器列表失败: {e}")
            return []

    def _create_new_browser(self, group_name: str) -> Optional[int]:
        """创建新浏览器实例"""
        config = {
            "group": group_name,
            "name": f"{group_name}_browser",
            "remark": f"Auto created for {group_name}",
        }
        return self.browser_api.add_browser(config)

    def _find_available_browser(self, group_name: str) -> Optional[dict]:
        """查找分组下可用的未启动浏览器"""
        browsers = self.browser_api.get_browser_list(group=group_name)
        running_browsers = self.browser_api.get_running_browsers()
        running_ids = {browser.get("id") for browser in running_browsers}

        # 找到未运行的浏览器
        for browser in browsers:
            if browser.get("id") not in running_ids:
                return browser
        return None

    async def _launch_and_get_port(self, browser_id: int) -> Optional[str]:
        """启动浏览器并获取调试端口"""
        response = await self.browser_api.launch_browser(browser_id)
        if response.get("success"):
            data = response.get("data", {})
            return str(data.get("debuggingPort")) if data.get("debuggingPort") else None
        return None

    @staticmethod
    def _cover_name(crawler_group: CrawlerPlatform):
        """
        将平台改为分组名称
        :param crawler_group:
        :return:
        """
        if crawler_group:
            return crawler_group.value
        else:
            return "默认分组"


if __name__ == "__main__":
    browser_helper: VirtualBrowserHelper = VirtualBrowserHelper(
        api_key="CJMZeL7iaMRpTIKWn44e2dmaw0jNaOQI"
    )

    platform_list: List[CrawlerPlatform] = [
        CrawlerPlatform.K_S,
        CrawlerPlatform.X_H_S,
        CrawlerPlatform.D_Y,
        CrawlerPlatform.W_B,
    ]

    asyncio.run(
        browser_helper.apply_browser(launch_new=True, crawler_group=CrawlerPlatform.K_S)
    )

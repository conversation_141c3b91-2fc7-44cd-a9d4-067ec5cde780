import uuid
import socket
import hashlib

def get_mac_address():
    """获取 MAC 地址"""
    try:
        mac = uuid.getnode()
        # 将 MAC 地址格式化为标准形式（如 00:1A:2B:3C:4D:5E）
        mac_address = ':'.join(['{:02x}'.format((mac >> elements) & 0xff) for elements in range(0, 8*6, 8)][::-1])
        return mac_address
    except Exception as e:
        return f"error_mac_{str(e)}"

def get_ip_address():
    """获取本机 IP 地址"""
    try:
        # 创建一个 socket 对象，连接到外部服务器以获取本地 IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))  # 使用 Google DNS 服务器
        ip_address = s.getsockname()[0]
        s.close()
        return ip_address
    except Exception as e:
        return f"error_ip_{str(e)}"

def generate_agent_id():
    """基于 MAC 地址和 IP 地址生成唯一编码"""
    # 获取 MAC 和 IP
    mac = get_mac_address()
    ip = get_ip_address()

    # 组合信息
    combined_info = f"{mac}|{ip}"

    # 使用 SHA-256 哈希生成固定长度编码
    hash_object = hashlib.sha256(combined_info.encode())
    unique_id = hash_object.hexdigest()

    return mac, ip, unique_id

def get_host_uuid():
    """获取设备唯一标识用于 API 调用"""
    _, _, unique_id = generate_agent_id()
    return unique_id

if __name__ == "__main__":
    mac_address, ip_address, unique_id = generate_agent_id()
    print(f"MAC 地址: {mac_address}")
    print(f"IP 地址: {ip_address}")
    print(f"唯一编码: {unique_id}")
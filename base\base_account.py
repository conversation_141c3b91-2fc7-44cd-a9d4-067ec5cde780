from dataclasses import dataclass
from abc import abstractmethod, ABC
from typing import Dict

@dataclass
class AccountInfo:
    user_uuid: str = None
    user_name: str = None
    agent_uuid: str = None
    host_name: str = None
    cookies: Dict = None
    platform: str = None
    ip_addr: str = None
    status: int = 0
    create_time: str = None
    update_time: str = None
    is_del: int = 0


class AbstractAccountStore(ABC):
    """
    AbstractAccountStore is used to repositories account info
    """
    @abstractmethod
    async def store_account(self, account: Dict):
        """
        Args:
            account:
        Returns:
        """

        pass

    @abstractmethod
    async def update_account(self, account: Dict):
        pass

    @abstractmethod
    async def get_account(self, account: Dict):
        pass

    @abstractmethod
    async def query_account_limit(self, agent_uuid: str, platform: str):
        """
        Args:
            agent_uuid:
            platform:
        Returns:
        查询企业
        """
        pass


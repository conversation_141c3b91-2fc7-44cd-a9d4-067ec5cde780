# -*- coding: utf-8 -*-

import asyncio
import os
import random
import time
from typing import List, Optional, Dict

from playwright.async_api import B<PERSON>er<PERSON>ontext, BrowserType, Page, async_playwright

import config
from crawlers.spiders.douyin.client import DOUYINClient
from crawlers.spiders.douyin.exception import DataFetchError
from crawlers.spiders.douyin.field import PublishTimeType, SearchChannelType, SearchSortType
from crawlers.spiders.douyin.login import DouYinLogin
from tools import utils


class DouyinSearch:
    """
    抖音搜索实现类
    """
    context_page: Page
    douyin_client: DOUYINClient
    browser_context: BrowserContext

    def __init__(self, headless: bool = True, use_local_chrome: bool = False, chrome_path: str = None,
                 user_data_dir: str = None) -> None:
        self.index_url = "https://www.douyin.com"
        self.user_agent = config.UA if config.UA else "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36"
        self.headless = headless
        self.use_local_chrome = use_local_chrome
        self.chrome_path = chrome_path
        self.user_data_dir = user_data_dir
        self.initialized = False

    async def initialize(self) -> None:
        """
        初始化浏览器和客户端
        """
        if self.initialized:
            return

        self.playwright = await async_playwright().start()
        # Launch a browser context.
        chromium = self.playwright.chromium
        self.browser_context = await self.launch_browser(
            chromium, self.user_agent, headless=self.headless
        )

        await self.browser_context.add_init_script(path="libs/stealth.min.js")
        self.context_page = await self.browser_context.new_page()

        await self.context_page.goto(self.index_url)
        httpx_proxy_format = None

        self.douyin_client = await self.create_douyin_client(httpx_proxy_format)
        if not await self.douyin_client.pong(browser_context=self.browser_context):
            login_obj = DouYinLogin(
                login_type="qrcode",
                login_phone="",
                browser_context=self.browser_context,
                context_page=self.context_page,
                cookie_str=config.COOKIES
            )
            await login_obj.begin()

            # 更新 cookie
            await self.douyin_client.update_cookies(
                browser_context=self.browser_context
            )

        self.initialized = True
        utils.logger.info("[DouyinSearch.initialize] Initialization completed")

    async def search(self, keyword: str, limit: int) -> list:
        # Log the beginning of the search
        utils.logger.info("[DouYinCrawler.search] Begin search douyin keywords")

        # Set the limit count to 10 if the limit is less than 10
        dy_limit_count = 10
        if limit < dy_limit_count:
            limit = dy_limit_count

        # Set the start page to 1
        start_page = 1

        # Initialize the result list
        res_list = []
        # Log the current keyword
        utils.logger.info(f"[DouYinCrawler.search] Current keyword: {keyword}")
        aweme_list: List[str] = []

        page = 0
        # Initialize the search id
        dy_search_id = ""
        # Loop until the number of results is less than the limit
        while (page - start_page + 1) * dy_limit_count <= limit:
            # Skip the page if it is less than the start page
            if page < start_page:
                utils.logger.info(f"[DouYinCrawler.search] Skip {page}")
                page += 1
                continue
            try:
                # Log the search keyword and page number
                utils.logger.info(f"[DouYinCrawler.search] search douyin keyword: {keyword}, page: {page}")

                # 执行搜索逻辑
                posts_res = await self.douyin_client.search_info_by_keyword_new(
                    search_channel=SearchChannelType.VIDEO,
                    sort_type=SearchSortType.GENERAL,
                    keyword=keyword,
                    offset=page * dy_limit_count - dy_limit_count,
                    publish_time=PublishTimeType(0),
                    search_id=dy_search_id
                )

                # 结束，无数据
                if posts_res.get("data") is None or posts_res.get("data") == []:
                    utils.logger.info(
                        f"[DouYinCrawler.search] search douyin keyword: {keyword}, page: {page} is empty,{posts_res.get('data')}`")
                    break

                res_list.extend(posts_res.get("data"))

            except DataFetchError:
                utils.logger.error(f"[DouYinCrawler.search] search douyin keyword: {keyword} failed")
                break

            # 检测是否超过 limit
            if len(res_list) >= limit:
                break

            page += 1
            if "data" not in posts_res:
                utils.logger.error(
                    f"[DouYinCrawler.search] search douyin keyword: {keyword} failed，账号也许被风控了。")
                break

            dy_search_id = posts_res.get("extra", {}).get("logid", "")

            # 添加短暂的翻页延迟
            await asyncio.sleep(random.uniform(2, 3))

        utils.logger.info(f"[DouYinCrawler.search] keyword:{keyword}, total:{len(aweme_list)}")
        return res_list

    async def add_account_monitor(self, home_url: str) -> None:
        """
        获取账号数据
        :param home_url:
        :return:
        """
        pass


    async def create_douyin_client(self, httpx_proxy: Optional[str]) -> DOUYINClient:
        """Create douyin client"""
        cookie_str, cookie_dict = utils.convert_cookies(await self.browser_context.cookies())  # type: ignore
        douyin_client = DOUYINClient(
            proxies=httpx_proxy,
            headers={
                "User-Agent": await self.context_page.evaluate("() => navigator.userAgent"),
                "Cookie": cookie_str,
                "Host": "www.douyin.com",
                "Origin": "https://www.douyin.com/",
                "Referer": "https://www.douyin.com/",
                "Content-Type": "application/json;charset=UTF-8"
            },
            playwright_page=self.context_page,
            cookie_dict=cookie_dict,
        )
        return douyin_client

    async def get_creators_today_videos(self, user_list: list[str],
                                        crawl_interval: float = 100.0) -> Dict:
        """
        获取当日作品数据
        """
        utils.logger.info("[DouYinCrawler.get_creators_and_videos] Begin get douyin creators")
        user_profile_list = {}
        for user_id in user_list:
            all_video_list = await self.douyin_client.get_all_user_aweme_posts(
                limit=20,
                sec_user_id=user_id
            )
            # handle today videos
            self._douyin_data(all_video_list)

            # user data
            user_profile_list[user_id] = all_video_list

            sl = random.uniform(50, 90)
            utils.logger.info(f"[DouYinCrawler.get_creators_and_videos] Sleep {sl} seconds")
            await asyncio.sleep(sl + crawl_interval)

        return user_profile_list

    def _douyin_data(self, all_video_list: List) -> List:

        current_time_struct = time.localtime(time.time())

        for awewe_video in all_video_list:
            time_struct = time.localtime(awewe_video['create_time'])
            if time_struct.tm_year == current_time_struct.tm_year \
                    and time_struct.tm_yday == current_time_struct.tm_yday:
                continue
            else:
                all_video_list.remove(awewe_video)
        utils.logger.info(f"[filter_today_data] : filter num {len(all_video_list)}")
        return all_video_list

    async def launch_browser(
            self,
            chromium: BrowserType,
            user_agent: Optional[str],
            headless: bool = True) -> BrowserContext:
        """启动浏览器并创建浏览器上下文"""
        utils.logger.info(
            "[DouyinSearch.launch_browser] Begin create browser context ..."
        )

        # 使用本地Chrome浏览器
        if self.use_local_chrome:
            utils.logger.info("[DouyinSearch.launch_browser] Using local Chrome browser")

            # 设置Chrome可执行文件路径
            executable_path = self.chrome_path

            # 设置用户数据目录
            user_data_dir = self.user_data_dir
            if not user_data_dir:
                # 如果未指定用户数据目录，使用默认目录
                user_data_dir = os.path.join(
                    os.getcwd(), "browser_data", config.USER_DATA_DIR % config.PLATFORM
                )  # type: ignore

            utils.logger.info(f"[DouyinSearch.launch_browser] Chrome path: {executable_path}")
            utils.logger.info(f"[DouyinSearch.launch_browser] User data directory: {user_data_dir}")

            # 启动Chrome浏览器
            browser_context = await chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                executable_path=executable_path if executable_path else None,
                accept_downloads=True,
                headless=headless,
                viewport={"width": 1920, "height": 1080},
                user_agent=user_agent,
            )
            return browser_context

        utils.logger.info("[DouyinSearch.launch_browser] Using local Chrome browser")

        # 设置用户数据目录
        user_data_dir = self.user_data_dir
        if not user_data_dir:
            # 如果未指定用户数据目录，使用默认目录
            user_data_dir = os.path.join(
                os.getcwd(), "browser_data", config.USER_DATA_DIR % config.PLATFORM
            )  # type: ignore

        utils.logger.info(f"[DouyinSearch.launch_browser] User data directory: {user_data_dir}")

        # 启动Chrome浏览器
        browser_context = await chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            accept_downloads=True,
            headless=headless,
            viewport={"width": 1920, "height": 1080},
            user_agent=user_agent,
        )
        return browser_context

    async def close(self):
        """关闭浏览器上下文"""
        if hasattr(self, 'browser_context'):
            await self.browser_context.close()
            utils.logger.info("[XiaoHongShuSearch.close] Browser context closed ...")

        if hasattr(self, 'playwright'):
            await self.playwright.stop()
            utils.logger.info("[XiaoHongShuSearch.close] Playwright stopped ...")

        self.initialized = False



if __name__ == "__main__":
    pass

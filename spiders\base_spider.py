# -*- coding: utf-8 -*-
"""
爬虫基类 - 定义统一的爬虫接口和通用功能
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass
from enum import Enum

from core.crawler_worker import CrawlerWorker


class CrawlType(Enum):
    """爬取类型枚举"""
    SEARCH = "search"       # 搜索爬取
    USER = "user"          # 用户爬取
    NOTE = "note"          # 笔记/内容爬取
    COMMENT = "comment"    # 评论爬取


@dataclass
class CrawlTask:
    """爬取任务数据模型"""
    task_id: str
    platform: str
    crawl_type: CrawlType
    keywords: Optional[str] = None
    user_id: Optional[str] = None
    note_id: Optional[str] = None
    limit: int = 10
    sort_type: Optional[str] = None
    extra_params: Optional[Dict[str, Any]] = None


@dataclass
class CrawlResult:
    """爬取结果数据模型"""
    task_id: str
    platform: str
    crawl_type: str
    success: bool
    data: List[Dict[str, Any]]
    total_count: int
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class BaseSpider(ABC):
    """
    爬虫基类 - 定义统一的爬虫接口
    """
    
    def __init__(self, platform: str):
        self.platform = platform
        self.worker: Optional[CrawlerWorker] = None
        
    async def set_worker(self, worker: CrawlerWorker):
        """设置爬虫工作单元"""
        self.worker = worker
        
    @abstractmethod
    async def search(self, 
                    keywords: str, 
                    limit: int = 10, 
                    sort_type: str = "综合排序") -> List[Dict[str, Any]]:
        """
        搜索爬取
        
        Args:
            keywords: 搜索关键词
            limit: 爬取数量限制
            sort_type: 排序类型
            
        Returns:
            List[Dict]: 爬取结果列表
        """
        pass
        
    @abstractmethod
    async def crawl_user(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        用户内容爬取
        
        Args:
            user_id: 用户ID
            limit: 爬取数量限制
            
        Returns:
            List[Dict]: 用户内容列表
        """
        pass
        
    @abstractmethod
    async def crawl_note(self, note_id: str) -> Dict[str, Any]:
        """
        单个笔记/内容爬取
        
        Args:
            note_id: 笔记/内容ID
            
        Returns:
            Dict: 笔记/内容详情
        """
        pass
        
    @abstractmethod
    async def crawl_comments(self, note_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        评论爬取
        
        Args:
            note_id: 笔记/内容ID
            limit: 爬取数量限制
            
        Returns:
            List[Dict]: 评论列表
        """
        pass
        
    async def execute_task(self, task: CrawlTask) -> CrawlResult:
        """
        执行爬取任务
        
        Args:
            task: 爬取任务
            
        Returns:
            CrawlResult: 爬取结果
        """
        try:
            if not self.worker:
                return CrawlResult(
                    task_id=task.task_id,
                    platform=task.platform,
                    crawl_type=task.crawl_type.value,
                    success=False,
                    data=[],
                    total_count=0,
                    error_message="未设置工作单元"
                )
                
            # 根据爬取类型执行相应的方法
            data = []
            if task.crawl_type == CrawlType.SEARCH:
                if not task.keywords:
                    raise ValueError("搜索模式需要提供关键词")
                data = await self.search(
                    keywords=task.keywords,
                    limit=task.limit,
                    sort_type=task.sort_type or "综合排序"
                )
                
            elif task.crawl_type == CrawlType.USER:
                if not task.user_id:
                    raise ValueError("用户模式需要提供用户ID")
                data = await self.crawl_user(
                    user_id=task.user_id,
                    limit=task.limit
                )
                
            elif task.crawl_type == CrawlType.NOTE:
                if not task.note_id:
                    raise ValueError("笔记模式需要提供笔记ID")
                note_data = await self.crawl_note(note_id=task.note_id)
                data = [note_data] if note_data else []
                
            elif task.crawl_type == CrawlType.COMMENT:
                if not task.note_id:
                    raise ValueError("评论模式需要提供笔记ID")
                data = await self.crawl_comments(
                    note_id=task.note_id,
                    limit=task.limit
                )
                
            return CrawlResult(
                task_id=task.task_id,
                platform=task.platform,
                crawl_type=task.crawl_type.value,
                success=True,
                data=data,
                total_count=len(data)
            )
            
        except Exception as e:
            return CrawlResult(
                task_id=task.task_id,
                platform=task.platform,
                crawl_type=task.crawl_type.value,
                success=False,
                data=[],
                total_count=0,
                error_message=str(e)
            )
            
    async def login(self) -> bool:
        """
        登录操作（可选实现）
        
        Returns:
            bool: 登录是否成功
        """
        return True
        
    async def check_login_status(self) -> bool:
        """
        检查登录状态（可选实现）
        
        Returns:
            bool: 是否已登录
        """
        return True
        
    async def cleanup(self):
        """
        清理资源（可选实现）
        """
        pass
        
    def get_platform(self) -> str:
        """获取平台名称"""
        return self.platform

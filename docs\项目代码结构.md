# MediaCrawler 项目结构

```
MediaCrawler/
├── api/                          # API接口目录
├── base/                         # 基础类定义
│   ├── base_account.py          # 账号基类
│   └── base_crawler.py          # 爬虫基类
├── cache/                        # 缓存相关实现
│   ├── abs_cache.py             # 缓存抽象类
│   ├── cache_factory.py         # 缓存工厂
│   ├── local_cache.py           # 本地缓存实现
│   └── redis_cache.py           # Redis缓存实现
├── common/                       # 公共模块
│   ├── crawler_platform.py      # 平台相关定义
│   └── var.py                   # 全局变量
├── config/                       # 配置目录
│   ├── base_config.py           # 基础配置
│   ├── db_config.py             # 数据库配置
│   ├── platform_specific_config.py  # 平台特定配置
│   └── risk_control_config.py   # 风控配置
├── crawlers/                     # 爬虫核心目录
│   ├── account/                 # 账号相关
│   │   ├── account_action.py    # 账号操作
│   │   └── account_store/       # 账号存储
│   ├── browser/                 # 浏览器模块
│   ├── constant/                # 常量定义
│   ├── libs/                    # JS脚本库
│   ├── manager/                 # 管理器模块
│   │   ├── account_manager.py   # 账号管理
│   │   ├── browser_manager.py   # 浏览器管理
│   │   ├── proxy_manager.py     # 代理管理
│   │   └── resource_manager.py  # 资源管理
│   ├── proxy/                   # 代理模块
│   │   └── providers/           # 代理供应商
│   └── spiders/                 # 各平台爬虫实现
│       ├── bilibili/           # B站爬虫
│       ├── douyin/             # 抖音爬虫
│       ├── kuaishou/           # 快手爬虫
│       ├── tieba/              # 贴吧爬虫
│       ├── weibo/              # 微博爬虫
│       ├── xhs/                # 小红书爬虫
│       └── zhihu/              # 知乎爬虫
├── database/                    # 数据库操作
├── docs/                        # 文档目录
├── model/                       # 数据模型
├── myScrapy/                    # 自定义爬虫框架
├── repositories/                # 数据仓库层
│   ├── account_repo.py         # 账号仓库
│   ├── async_db.py             # 异步数据库操作
│   ├── bilibili/               # B站数据存储
│   ├── douyin/                 # 抖音数据存储
│   ├── kuaishou/               # 快手数据存储
│   ├── tieba/                  # 贴吧数据存储
│   ├── weibo/                  # 微博数据存储
│   ├── xhs/                    # 小红书数据存储
│   └── zhihu/                  # 知乎数据存储
├── schema/                      # 数据库schema
├── services/                    # 服务层
├── test/                        # 测试用例
└── tools/                       # 工具类

主要配置文件：
├── main.py                      # 主入口文件
├── mypy.ini                     # mypy配置
├── pyproject.toml              # 项目配置
└── requirements.txt            # 依赖包列表
```

## 目录说明

1. `api/` - 提供外部接口的定义和实现
2. `base/` - 包含基础类定义，如账号基类和爬虫基类
3. `cache/` - 缓存相关实现，支持本地缓存和Redis缓存
4. `common/` - 公共模块，包含平台定义和全局变量
5. `config/` - 配置文件目录
   - 基础配置、数据库配置
   - 平台特定配置
   - 风控配置
6. `crawlers/` - 爬虫核心实现目录
   - `account/` - 账号操作和存储
   - `browser/` - 浏览器自动化相关实现
   - `manager/` - 各类资源管理器
   - `proxy/` - 代理IP池管理
   - `spiders/` - 各平台爬虫具体实现
7. `database/` - 数据库操作封装
8. `model/` - 数据模型定义
9. `repositories/` - 数据持久层实现
10. `services/` - 业务服务层
11. `test/` - 单元测试和集成测试
12. `tools/` - 通用工具类

## 主要功能模块

1. **多平台管理**：
   - 支持抖音、快手、小红书、B站等主流平台
   - 平台特定配置管理
   - 风控策略配置

2. **资源管理**：
   - 账号池管理
   - 浏览器资源管理
   - 代理资源管理
   - 通用资源管理器

3. **数据采集**：
   - 多平台爬虫实现
   - 统一的爬虫基类
   - 平台特定的API封装

4. **风控处理**：
   - 风控配置管理
   - 代理IP轮换
   - 账号调度策略

5. **数据存储**：
   - 异步数据库操作
   - 分平台数据仓库
   - 统一的数据模型

6. **系统服务**：
   - 缓存服务
   - 账号服务
   - 代理服务
   - 数据服务
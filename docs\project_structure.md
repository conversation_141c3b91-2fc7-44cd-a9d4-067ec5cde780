# MediaCrawler 项目结构

本文档展示了MediaCrawler项目的整体架构和模块关系。

## 项目结构图

```mermaid
graph TB
    A[MediaCrawler] --> B[crawlers]
    A --> C[repositories]
    A --> D[database]
    A --> E[cache]
    A --> F[config]
    A --> G[tools]
    A --> H[model]
    A --> I[docs]

    %% crawlers模块详情
    B --> B1[spiders]
    B --> B2[browser]
    B --> B3[proxy]
    B --> B4[account_manager]
    
    %% spiders子模块
    B1 --> SP1[douyin]
    B1 --> SP2[xhs]
    B1 --> SP3[bilibili]
    B1 --> SP4[kuaishou]
    B1 --> SP5[weibo]
    B1 --> SP6[zhihu]
    B1 --> SP7[tieba]

    %% browser子模块
    B2 --> BR1[virtual_browser_helper]
    B2 --> BR2[cookies_pool]

    %% proxy子模块
    B3 --> PR1[proxy_ip_pool]
    B3 --> PR2[providers]

    %% repositories模块详情
    C --> R1[douyin]
    C --> R2[xhs]
    C --> R3[bilibili]
    C --> R4[kuaishou]
    C --> R5[weibo]
    C --> R6[zhihu]
    C --> R7[tieba]

    %% cache模块详情
    E --> E1[local_cache]
    E --> E2[redis_cache]

    classDef default fill:#f9f,stroke:#333,stroke-width:2px;
    classDef module fill:#bbf,stroke:#333,stroke-width:2px;
    classDef submodule fill:#ddf,stroke:#333,stroke-width:2px;

    class A default;
    class B,C,D,E,F,G,H,I module;
    class B1,B2,B3,B4,SP1,SP2,SP3,SP4,SP5,SP6,SP7,BR1,BR2,PR1,PR2,R1,R2,R3,R4,R5,R6,R7,E1,E2 submodule;

```

## 核心模块说明

### 1. crawlers
- **spiders**: 各平台爬虫实现
  - douyin: 抖音平台爬虫
  - xhs: 小红书平台爬虫
  - bilibili: B站平台爬虫
  - kuaishou: 快手平台爬虫
  - weibo: 微博平台爬虫
  - zhihu: 知乎平台爬虫
  - tieba: 贴吧平台爬虫
- **browser**: 浏览器模拟相关
  - virtual_browser_helper: 虚拟浏览器助手
  - cookies_pool: Cookie池管理
- **proxy**: 代理IP管理
  - proxy_ip_pool: 代理IP池
  - providers: 不同代理提供商的实现

### 2. repositories
各平台数据存储实现，包括：
- 数据持久化
- SQL语句管理
- 数据模型映射

### 3. cache
缓存实现：
- local_cache: 本地缓存实现
- redis_cache: Redis缓存实现

### 4. config
配置管理：
- base_config: 基础配置
- db_config: 数据库配置

### 5. tools
通用工具类：
- crawler_util: 爬虫工具类
- time_util: 时间处理工具
- utils: 通用工具方法

### 6. model
各平台数据模型定义

### 7. docs
项目文档
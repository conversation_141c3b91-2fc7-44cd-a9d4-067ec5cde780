# -*- coding: utf-8 -*-
"""
爬虫工作单元 - 核心组件，组合浏览器、账号、代理等资源
"""
import asyncio
import uuid
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from core.account_manager import AccountManager, Account
from core.browser_manager import BrowserManager, BrowserInstance
from core.proxy_manager import ProxyManager, ProxyInfo


class WorkerStatus(Enum):
    """工作单元状态枚举"""

    IDLE = "idle"  # 空闲
    INITIALIZING = "initializing"  # 初始化中
    WORKING = "working"  # 工作中
    ERROR = "error"  # 错误
    STOPPED = "stopped"  # 已停止


@dataclass
class CrawlerWorker:
    """
    爬虫工作单元 - 组合浏览器、账号、代理等资源执行爬取任务
    """

    worker_id: str
    platform: str
    status: WorkerStatus = WorkerStatus.IDLE
    account: Optional[Account] = None
    browser_instance: Optional[BrowserInstance] = None
    proxy: Optional[ProxyInfo] = None
    created_at: Optional[datetime] = None
    last_used: Optional[datetime] = None
    error_message: Optional[str] = None


class CrawlerWorkerManager:
    """
    爬虫工作单元管理器
    """

    def __init__(
        self,
        account_manager: AccountManager,
        browser_manager: BrowserManager,
        proxy_manager: ProxyManager,
    ):
        self.account_manager = account_manager
        self.browser_manager = browser_manager
        self.proxy_manager = proxy_manager

        self.workers: Dict[str, CrawlerWorker] = {}
        self._lock = asyncio.Lock()

    async def create_worker(
        self, platform: str, use_account: bool = True, use_proxy: bool = False
    ) -> Optional[CrawlerWorker]:
        """创建爬虫工作单元"""
        async with self._lock:
            worker_id = str(uuid.uuid4())

            worker = CrawlerWorker(
                worker_id=worker_id,
                platform=platform,
                status=WorkerStatus.INITIALIZING,
                created_at=datetime.now(),
            )

            try:
                # 获取账号
                if use_account:
                    account = await self.account_manager.get_available_account(platform)
                    if not account:
                        worker.status = WorkerStatus.ERROR
                        worker.error_message = "无可用账号"
                        return worker
                    worker.account = account

                # 获取代理
                proxy = None
                if use_proxy:
                    proxy = await self.proxy_manager.get_proxy()
                    if not proxy:
                        # 释放已分配的账号
                        if worker.account:
                            await self.account_manager.release_account(
                                platform, worker.account.user_id, success=False
                            )
                        worker.status = WorkerStatus.ERROR
                        worker.error_message = "无可用代理"
                        return worker
                    worker.proxy = proxy

                # 获取浏览器实例
                browser_instance = await self.browser_manager.get_browser_instance(
                    platform=platform,
                    proxy=proxy.to_playwright_proxy() if proxy else None,
                    user_agent=None,  # 可以根据需要设置
                )

                if not browser_instance:
                    # 释放已分配的资源
                    if worker.account:
                        await self.account_manager.release_account(
                            platform, worker.account.user_id, success=False
                        )
                    if worker.proxy:
                        await self.proxy_manager.release_proxy(
                            worker.proxy, success=False
                        )

                    worker.status = WorkerStatus.ERROR
                    worker.error_message = "无可用浏览器实例"
                    return worker

                worker.browser_instance = browser_instance
                worker.status = WorkerStatus.IDLE

                self.workers[worker_id] = worker
                return worker

            except Exception as e:
                worker.status = WorkerStatus.ERROR
                worker.error_message = f"创建工作单元失败: {str(e)}"
                return worker

    async def get_worker(self, worker_id: str) -> Optional[CrawlerWorker]:
        """获取工作单元"""
        return self.workers.get(worker_id)

    async def start_worker(self, worker_id: str) -> bool:
        """启动工作单元"""
        async with self._lock:
            worker = self.workers.get(worker_id)
            if not worker or worker.status != WorkerStatus.IDLE:
                return False

            worker.status = WorkerStatus.WORKING
            worker.last_used = datetime.now()
            return True

    async def stop_worker(self, worker_id: str, success: bool = True) -> bool:
        """停止工作单元"""
        async with self._lock:
            worker = self.workers.get(worker_id)
            if not worker:
                return False

            worker.status = WorkerStatus.STOPPED

            # 释放资源
            if worker.account:
                await self.account_manager.release_account(
                    worker.platform, worker.account.user_id, success=success
                )

            if worker.proxy:
                await self.proxy_manager.release_proxy(worker.proxy, success=success)

            if worker.browser_instance:
                await self.browser_manager.release_browser_instance(
                    worker.browser_instance
                )

            return True

    async def destroy_worker(self, worker_id: str):
        """销毁工作单元"""
        await self.stop_worker(worker_id, success=False)

        async with self._lock:
            worker = self.workers.get(worker_id)
            if worker and worker.browser_instance:
                await self.browser_manager.close_browser_instance(
                    worker.browser_instance
                )

            # 从管理器中移除
            if worker_id in self.workers:
                del self.workers[worker_id]

    async def get_worker_status(self, platform: Optional[str] = None) -> Dict[str, int]:
        """获取工作单元状态统计"""
        status_count = {status.value: 0 for status in WorkerStatus}

        for worker in self.workers.values():
            if platform is None or worker.platform == platform:
                status_count[worker.status.value] += 1

        return status_count

    async def cleanup_stopped_workers(self):
        """清理已停止的工作单元"""
        stopped_workers = [
            worker_id
            for worker_id, worker in self.workers.items()
            if worker.status == WorkerStatus.STOPPED
        ]

        for worker_id in stopped_workers:
            await self.destroy_worker(worker_id)

    async def get_available_worker(self, platform: str) -> Optional[CrawlerWorker]:
        """获取指定平台的可用工作单元"""
        for worker in self.workers.values():
            if worker.platform == platform and worker.status == WorkerStatus.IDLE:
                return worker
        return None

    def get_worker_count(self, platform: Optional[str] = None) -> int:
        """获取工作单元总数"""
        if platform is None:
            return len(self.workers)
        return len([w for w in self.workers.values() if w.platform == platform])

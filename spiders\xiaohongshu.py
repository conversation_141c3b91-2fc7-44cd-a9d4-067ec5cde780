# -*- coding: utf-8 -*-
"""
小红书爬虫实现
"""
import asyncio
import random
from typing import Dict, List, Optional, Any

from spiders.base_spider import BaseSpider
from crawlers.spiders.xhs.client import XHSClient
from crawlers.spiders.xhs.login import XiaoHongShuLogin
from crawlers.spiders.xhs.exception import DataFetchError
from tools import utils


class XiaohongshuSpider(BaseSpider):
    """
    小红书爬虫实现
    """
    
    def __init__(self):
        super().__init__("xiaohongshu")
        self.index_url = "https://www.xiaohongshu.com"
        self.xhs_client: Optional[XHSClient] = None
        
    async def _initialize_client(self):
        """初始化小红书客户端"""
        if not self.worker or not self.worker.browser_instance:
            raise ValueError("未设置工作单元或浏览器实例")
            
        page = self.worker.browser_instance.page
        context = self.worker.browser_instance.context
        
        # 导航到小红书首页
        await page.goto(self.index_url)
        
        # 创建客户端
        cookie_str, cookie_dict = utils.convert_cookies(await context.cookies())
        
        proxy_config = None
        if self.worker.proxy:
            proxy_config = {
                f"{self.worker.proxy.protocol}": self.worker.proxy.proxy_url
            }
            
        self.xhs_client = XHSClient(
            proxies=proxy_config,
            headers={
                "User-Agent": await page.evaluate("() => navigator.userAgent"),
                "Cookie": cookie_str,
                "Origin": "https://www.xiaohongshu.com",
                "Referer": "https://www.xiaohongshu.com/",
                "Content-Type": "application/json;charset=UTF-8"
            },
            playwright_page=page,
            cookie_dict=cookie_dict,
        )
        
    async def login(self) -> bool:
        """登录小红书"""
        try:
            if not self.worker or not self.worker.browser_instance:
                return False
                
            await self._initialize_client()
            
            # 检查是否已登录
            if await self.xhs_client.pong(browser_context=self.worker.browser_instance.context):
                return True
                
            # 执行登录
            login_obj = XiaoHongShuLogin(
                login_type="qrcode",  # 默认使用二维码登录
                login_phone="",
                browser_context=self.worker.browser_instance.context,
                context_page=self.worker.browser_instance.page,
                cookie_str=""
            )
            
            await login_obj.begin()
            await self.xhs_client.update_cookies(browser_context=self.worker.browser_instance.context)
            
            return True
            
        except Exception as e:
            utils.logger.error(f"小红书登录失败: {e}")
            return False
            
    async def search(self, 
                    keywords: str, 
                    limit: int = 10, 
                    sort_type: str = "综合排序") -> List[Dict[str, Any]]:
        """搜索小红书内容"""
        try:
            if not self.xhs_client:
                await self._initialize_client()
                
            results = []
            page = 1
            
            while len(results) < limit:
                try:
                    utils.logger.info(f"搜索小红书关键词: {keywords}, 页码: {page}")
                    
                    # 调用搜索API
                    posts_res = await self.xhs_client.get_note_by_keyword(
                        keyword=keywords,
                        page=page,
                        sort=sort_type
                    )
                    
                    if not posts_res.get("items"):
                        break
                        
                    for note_item in posts_res.get("items", []):
                        if len(results) >= limit:
                            break
                            
                        try:
                            # 格式化数据
                            formatted_data = self._format_note_data(note_item)
                            results.append(formatted_data)
                                
                        except Exception as e:
                            utils.logger.error(f"格式化笔记数据失败: {e}")
                            continue
                            
                    page += 1
                    
                    # 添加随机延迟
                    await asyncio.sleep(random.uniform(1, 3))
                    
                except DataFetchError as e:
                    utils.logger.error(f"搜索小红书关键词失败: {keywords}, 错误: {e}")
                    break
                    
            return results[:limit]
            
        except Exception as e:
            utils.logger.error(f"小红书搜索异常: {e}")
            return []
            
    async def crawl_user(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """爬取用户内容"""
        try:
            if not self.xhs_client:
                await self._initialize_client()
                
            # 获取用户信息
            user_info = await self.xhs_client.get_user_info(user_id)
            
            # 获取用户笔记列表
            notes_res = await self.xhs_client.get_user_notes(
                user_id=user_id,
                cursor=""
            )
            
            results = []
            for note_item in notes_res.get("items", [])[:limit]:
                try:
                    note_id = note_item.get("note_card", {}).get("note_id")
                    if note_id:
                        note_detail = await self.xhs_client.get_note_by_id(note_id)
                        if note_detail:
                            formatted_data = self._format_note_data(note_detail)
                            results.append(formatted_data)
                            
                except Exception as e:
                    utils.logger.error(f"获取笔记详情失败: {e}")
                    continue
                    
            return results
            
        except Exception as e:
            utils.logger.error(f"爬取用户内容异常: {e}")
            return []
            
    async def crawl_note(self, note_id: str) -> Dict[str, Any]:
        """爬取单个笔记详情"""
        try:
            if not self.xhs_client:
                await self._initialize_client()
                
            note_detail = await self.xhs_client.get_note_by_id(note_id)
            if note_detail:
                return self._format_note_data(note_detail)
                
            return {}
            
        except Exception as e:
            utils.logger.error(f"爬取笔记详情异常: {e}")
            return {}
            
    async def crawl_comments(self, note_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """爬取笔记评论"""
        try:
            if not self.xhs_client:
                await self._initialize_client()
                
            comments = []
            cursor = ""
            
            while len(comments) < limit:
                try:
                    comments_res = await self.xhs_client.get_note_comments(
                        note_id=note_id,
                        cursor=cursor
                    )
                    
                    if not comments_res.get("comments"):
                        break
                        
                    for comment_item in comments_res.get("comments", []):
                        if len(comments) >= limit:
                            break
                            
                        formatted_comment = self._format_comment_data(comment_item)
                        comments.append(formatted_comment)
                        
                    cursor = comments_res.get("cursor", "")
                    if not cursor:
                        break
                        
                    # 添加随机延迟
                    await asyncio.sleep(random.uniform(1, 2))
                    
                except Exception as e:
                    utils.logger.error(f"获取评论失败: {e}")
                    break
                    
            return comments[:limit]
            
        except Exception as e:
            utils.logger.error(f"爬取评论异常: {e}")
            return []
            
    def _format_note_data(self, note_info: Dict[str, Any]) -> Dict[str, Any]:
        """格式化小红书笔记数据"""
        try:
            note_card = note_info.get("note_card", {})
            user_info = note_card.get("user", {})
            interact_info = note_card.get("interact_info", {})
            
            return {
                "note_id": note_card.get("note_id", ""),
                "title": note_card.get("display_title", ""),
                "desc": note_card.get("desc", ""),
                "type": note_card.get("type", ""),
                "time": note_card.get("time", 0),
                "author": {
                    "user_id": user_info.get("user_id", ""),
                    "nickname": user_info.get("nickname", ""),
                    "avatar": user_info.get("avatar", "")
                },
                "interact_info": {
                    "liked_count": interact_info.get("liked_count", "0"),
                    "collected_count": interact_info.get("collected_count", "0"),
                    "comment_count": interact_info.get("comment_count", "0"),
                    "share_count": interact_info.get("share_count", "0")
                },
                "image_list": note_card.get("image_list", []),
                "tag_list": note_card.get("tag_list", []),
                "platform": "xiaohongshu",
                "raw_data": note_info
            }
        except Exception as e:
            utils.logger.error(f"格式化小红书数据失败: {e}")
            return {"raw_data": note_info}
            
    def _format_comment_data(self, comment_info: Dict[str, Any]) -> Dict[str, Any]:
        """格式化评论数据"""
        try:
            return {
                "comment_id": comment_info.get("id", ""),
                "content": comment_info.get("content", ""),
                "create_time": comment_info.get("create_time", 0),
                "user_info": {
                    "user_id": comment_info.get("user_info", {}).get("user_id", ""),
                    "nickname": comment_info.get("user_info", {}).get("nickname", ""),
                    "avatar": comment_info.get("user_info", {}).get("image", "")
                },
                "like_count": comment_info.get("like_count", 0),
                "sub_comment_count": comment_info.get("sub_comment_count", 0),
                "platform": "xiaohongshu",
                "raw_data": comment_info
            }
        except Exception as e:
            utils.logger.error(f"格式化评论数据失败: {e}")
            return {"raw_data": comment_info}

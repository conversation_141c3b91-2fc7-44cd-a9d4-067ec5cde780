# -*- coding: utf-8 -*-
"""
抖音爬虫实现
"""
import asyncio
import random
from typing import Dict, List, Optional, Any

from spiders.base_spider import BaseSpider
from crawlers.spiders.douyin.client import DOUYINClient
from crawlers.spiders.douyin.field import PublishTimeType
from crawlers.spiders.douyin.login import DouYinLogin
from crawlers.spiders.douyin.exception import DataFetchError
from tools import utils


class DouyinSpider(BaseSpider):
    """
    抖音爬虫实现
    """
    
    def __init__(self):
        super().__init__("douyin")
        self.index_url = "https://www.douyin.com"
        self.dy_client: Optional[DOUYINClient] = None
        
    async def _initialize_client(self):
        """初始化抖音客户端"""
        if not self.worker or not self.worker.browser_instance:
            raise ValueError("未设置工作单元或浏览器实例")
            
        page = self.worker.browser_instance.page
        context = self.worker.browser_instance.context
        
        # 导航到抖音首页
        await page.goto(self.index_url)
        
        # 创建客户端
        cookie_str, cookie_dict = utils.convert_cookies(await context.cookies())
        
        proxy_config = None
        if self.worker.proxy:
            proxy_config = {
                f"{self.worker.proxy.protocol}": self.worker.proxy.proxy_url
            }
            
        self.dy_client = DOUYINClient(
            proxies=proxy_config,
            headers={
                "User-Agent": await page.evaluate("() => navigator.userAgent"),
                "Cookie": cookie_str,
                "Host": "www.douyin.com",
                "Origin": "https://www.douyin.com/",
                "Referer": "https://www.douyin.com/",
                "Content-Type": "application/json;charset=UTF-8"
            },
            playwright_page=page,
            cookie_dict=cookie_dict,
        )
        
    async def login(self) -> bool:
        """登录抖音"""
        try:
            if not self.worker or not self.worker.browser_instance:
                return False
                
            await self._initialize_client()
            
            # 检查是否已登录
            if await self.dy_client.pong(browser_context=self.worker.browser_instance.context):
                return True
                
            # 执行登录
            login_obj = DouYinLogin(
                login_type="qrcode",  # 默认使用二维码登录
                login_phone="",
                browser_context=self.worker.browser_instance.context,
                context_page=self.worker.browser_instance.page,
                cookie_str=""
            )
            
            await login_obj.begin()
            await self.dy_client.update_cookies(browser_context=self.worker.browser_instance.context)
            
            return True
            
        except Exception as e:
            utils.logger.error(f"抖音登录失败: {e}")
            return False
            
    async def search(self, 
                    keywords: str, 
                    limit: int = 10, 
                    sort_type: str = "综合排序") -> List[Dict[str, Any]]:
        """搜索抖音内容"""
        try:
            if not self.dy_client:
                await self._initialize_client()
                
            results = []
            dy_limit_count = 10  # 抖音每页固定数量
            page = 0
            dy_search_id = ""
            
            while len(results) < limit:
                try:
                    utils.logger.info(f"搜索抖音关键词: {keywords}, 页码: {page}")
                    
                    posts_res = await self.dy_client.search_info_by_keyword(
                        keyword=keywords,
                        offset=page * dy_limit_count,
                        publish_time=PublishTimeType.UNLIMITED,
                        search_id=dy_search_id
                    )
                    
                    if not posts_res.get("data"):
                        break
                        
                    dy_search_id = posts_res.get("extra", {}).get("logid", "")
                    
                    for post_item in posts_res.get("data", []):
                        if len(results) >= limit:
                            break
                            
                        try:
                            aweme_info = (post_item.get("aweme_info") or 
                                        post_item.get("aweme_mix_info", {}).get("mix_items", [{}])[0])
                            
                            if aweme_info:
                                # 格式化数据
                                formatted_data = self._format_aweme_data(aweme_info)
                                results.append(formatted_data)
                                
                        except (TypeError, IndexError):
                            continue
                            
                    page += 1
                    
                    # 添加随机延迟
                    await asyncio.sleep(random.uniform(1, 3))
                    
                except DataFetchError as e:
                    utils.logger.error(f"搜索抖音关键词失败: {keywords}, 错误: {e}")
                    break
                    
            return results[:limit]
            
        except Exception as e:
            utils.logger.error(f"抖音搜索异常: {e}")
            return []
            
    async def crawl_user(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """爬取用户内容"""
        try:
            if not self.dy_client:
                await self._initialize_client()
                
            # 获取用户信息
            creator_info = await self.dy_client.get_user_info(user_id)
            
            # 获取用户视频列表
            video_list = await self.dy_client.get_all_user_aweme_posts(
                sec_user_id=user_id,
                callback=None
            )
            
            results = []
            for video_item in video_list[:limit]:
                try:
                    aweme_id = video_item.get("aweme_id")
                    if aweme_id:
                        aweme_detail = await self.dy_client.get_video_by_id(aweme_id)
                        if aweme_detail:
                            formatted_data = self._format_aweme_data(aweme_detail)
                            results.append(formatted_data)
                            
                except Exception as e:
                    utils.logger.error(f"获取视频详情失败: {e}")
                    continue
                    
            return results
            
        except Exception as e:
            utils.logger.error(f"爬取用户内容异常: {e}")
            return []
            
    async def crawl_note(self, note_id: str) -> Dict[str, Any]:
        """爬取单个视频详情"""
        try:
            if not self.dy_client:
                await self._initialize_client()
                
            aweme_detail = await self.dy_client.get_video_by_id(note_id)
            if aweme_detail:
                return self._format_aweme_data(aweme_detail)
                
            return {}
            
        except Exception as e:
            utils.logger.error(f"爬取视频详情异常: {e}")
            return {}
            
    async def crawl_comments(self, note_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """爬取视频评论"""
        try:
            if not self.dy_client:
                await self._initialize_client()
                
            comments = []
            
            # 使用现有的评论爬取方法
            await self.dy_client.get_aweme_all_comments(
                aweme_id=note_id,
                crawl_interval=random.uniform(1, 3),
                is_fetch_sub_comments=False,
                callback=lambda comment_list: comments.extend(comment_list),
                max_count=limit
            )
            
            return comments[:limit]
            
        except Exception as e:
            utils.logger.error(f"爬取评论异常: {e}")
            return []
            
    def _format_aweme_data(self, aweme_info: Dict[str, Any]) -> Dict[str, Any]:
        """格式化抖音数据"""
        try:
            return {
                "aweme_id": aweme_info.get("aweme_id", ""),
                "desc": aweme_info.get("desc", ""),
                "create_time": aweme_info.get("create_time", 0),
                "author": {
                    "uid": aweme_info.get("author", {}).get("uid", ""),
                    "nickname": aweme_info.get("author", {}).get("nickname", ""),
                    "avatar": aweme_info.get("author", {}).get("avatar_thumb", {}).get("url_list", [""])[0]
                },
                "statistics": {
                    "digg_count": aweme_info.get("statistics", {}).get("digg_count", 0),
                    "comment_count": aweme_info.get("statistics", {}).get("comment_count", 0),
                    "share_count": aweme_info.get("statistics", {}).get("share_count", 0),
                    "play_count": aweme_info.get("statistics", {}).get("play_count", 0)
                },
                "video": {
                    "play_addr": aweme_info.get("video", {}).get("play_addr", {}).get("url_list", [""])[0],
                    "cover": aweme_info.get("video", {}).get("cover", {}).get("url_list", [""])[0],
                    "duration": aweme_info.get("video", {}).get("duration", 0)
                },
                "platform": "douyin",
                "raw_data": aweme_info
            }
        except Exception as e:
            utils.logger.error(f"格式化抖音数据失败: {e}")
            return {"raw_data": aweme_info}

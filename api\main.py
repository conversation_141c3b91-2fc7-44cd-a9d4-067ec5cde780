# -*- coding: utf-8 -*-
"""
FastAPI 应用主入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from api.endpoints import crawl_task, system_status
from config.settings import settings


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    print("🚀 MediaCrawler API 服务启动中...")
    
    # 这里可以添加数据库初始化等启动逻辑
    # await init_database()
    
    yield
    
    # 关闭时清理
    print("🛑 MediaCrawler API 服务关闭中...")
    # await close_database()


def create_app() -> FastAPI:
    """创建 FastAPI 应用实例"""
    app = FastAPI(
        title="MediaCrawler API",
        description="社交媒体爬虫服务 API",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # 配置 CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(crawl_task.router, prefix="/api/v1", tags=["爬取任务"])
    app.include_router(system_status.router, prefix="/api/v1", tags=["系统状态"])
    
    @app.get("/")
    async def root():
        return {"message": "MediaCrawler API 服务运行中", "version": "1.0.0"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "MediaCrawler API"}
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )

# -*- coding: utf-8 -*-
"""
爬取任务 API 端点
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field

from services.crawl_service import CrawlService
from common.crawler_platform import CrawlerPlatform


router = APIRouter()


class CrawlTaskRequest(BaseModel):
    """爬取任务请求模型"""
    platform: str = Field(..., description="平台名称 (xhs, dy, ks, bili, wb, tieba, zhihu)")
    keywords: Optional[str] = Field(None, description="搜索关键词")
    limit: int = Field(10, ge=1, le=100, description="爬取数量限制")
    sort_type: Optional[str] = Field("综合排序", description="排序类型")
    crawl_type: str = Field("search", description="爬取类型 (search, user, note)")
    user_id: Optional[str] = Field(None, description="用户ID (当 crawl_type=user 时必填)")
    note_id: Optional[str] = Field(None, description="笔记ID (当 crawl_type=note 时必填)")
    enable_login: bool = Field(False, description="是否启用登录")
    use_proxy: bool = Field(False, description="是否使用代理")


class CrawlTaskResponse(BaseModel):
    """爬取任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")


class CrawlResultResponse(BaseModel):
    """爬取结果响应模型"""
    task_id: str
    status: str
    platform: str
    total_count: int
    data: List[dict]
    error_message: Optional[str] = None


def get_crawl_service() -> CrawlService:
    """依赖注入：获取爬取服务实例"""
    return CrawlService()


@router.post("/crawl", response_model=CrawlTaskResponse)
async def create_crawl_task(
    request: CrawlTaskRequest,
    background_tasks: BackgroundTasks,
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    创建爬取任务
    """
    try:
        # 验证平台参数
        if request.platform not in ["xhs", "dy", "ks", "bili", "wb", "tieba", "zhihu"]:
            raise HTTPException(status_code=400, detail="不支持的平台")
        
        # 验证爬取类型参数
        if request.crawl_type == "user" and not request.user_id:
            raise HTTPException(status_code=400, detail="用户爬取模式需要提供 user_id")
        
        if request.crawl_type == "note" and not request.note_id:
            raise HTTPException(status_code=400, detail="笔记爬取模式需要提供 note_id")
        
        if request.crawl_type == "search" and not request.keywords:
            raise HTTPException(status_code=400, detail="搜索模式需要提供关键词")
        
        # 创建爬取任务
        task_id = await crawl_service.create_crawl_task(
            platform=request.platform,
            crawl_type=request.crawl_type,
            keywords=request.keywords,
            user_id=request.user_id,
            note_id=request.note_id,
            limit=request.limit,
            sort_type=request.sort_type,
            enable_login=request.enable_login,
            use_proxy=request.use_proxy
        )
        
        # 在后台执行爬取任务
        background_tasks.add_task(crawl_service.execute_crawl_task, task_id)
        
        return CrawlTaskResponse(
            task_id=task_id,
            status="created",
            message="爬取任务已创建，正在后台执行"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建爬取任务失败: {str(e)}")


@router.get("/crawl/{task_id}", response_model=CrawlResultResponse)
async def get_crawl_result(
    task_id: str,
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    获取爬取任务结果
    """
    try:
        result = await crawl_service.get_crawl_result(task_id)
        if not result:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取爬取结果失败: {str(e)}")


@router.get("/crawl/{task_id}/status")
async def get_crawl_status(
    task_id: str,
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    获取爬取任务状态
    """
    try:
        status = await crawl_service.get_task_status(task_id)
        if not status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return {"task_id": task_id, "status": status}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.delete("/crawl/{task_id}")
async def cancel_crawl_task(
    task_id: str,
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    取消爬取任务
    """
    try:
        success = await crawl_service.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"task_id": task_id, "message": "任务已取消"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/crawl")
async def list_crawl_tasks(
    status: Optional[str] = None,
    platform: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    获取爬取任务列表
    """
    try:
        tasks = await crawl_service.list_tasks(
            status=status,
            platform=platform,
            limit=limit,
            offset=offset
        )
        
        return {
            "tasks": tasks,
            "total": len(tasks),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

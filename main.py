import asyncio
import sys

import config
from base.base_crawler import <PERSON>bs<PERSON><PERSON>raw<PERSON>
from crawlers.spiders.bilibili import B<PERSON>biliCrawler
from crawlers.spiders.douyin import DouYin<PERSON>rawler
from crawlers.spiders.kuaishou import <PERSON><PERSON>hou<PERSON>rawler
from crawlers.spiders.tieba import TieBaCrawler
from crawlers.spiders.weibo import WeiboCrawler
from crawlers.spiders.xhs import XiaoHongShuCrawler
from crawlers.spiders.zhihu import ZhihuCrawler


class CrawlerFactory:
    CRAWLERS = {
        "xhs": <PERSON><PERSON>ongShuCrawler,
        "dy": <PERSON><PERSON><PERSON><PERSON><PERSON>raw<PERSON>,
        "ks": <PERSON><PERSON><PERSON><PERSON>raw<PERSON>,
        "bili": <PERSON><PERSON>biliCrawler,
        "wb": <PERSON><PERSON><PERSON>raw<PERSON>,
        "tieba": <PERSON><PERSON><PERSON>a<PERSON>raw<PERSON>,
        "zhihu": <PERSON><PERSON><PERSON><PERSON>raw<PERSON>
    }

    @staticmethod
    def create_crawler(platform: str) -> AbstractCrawler:
        crawler_class = CrawlerFactory.CRAWLERS.get(platform)
        if not crawler_class:
            raise ValueError("Invalid Media Platform Currently only supported xhs or dy or ks or bili ...")
        return crawler_class()


async def main():
    # init db
    # if config.SAVE_DATA_OPTION == "db":
    #     await db.init_db()

    crawler = CrawlerFactory.create_crawler(platform=config.PLATFORM)
    await crawler.start()

    # if config.SAVE_DATA_OPTION == "db":
    #     await db.close()


if __name__ == '__main__':
    try:
        # asyncio.run(main())
        asyncio.get_event_loop().run_until_complete(main())
    except KeyboardInterrupt:
        sys.exit()

import json

from pydantic import BaseModel


class AccountModel(BaseModel):
    """
    登陆账号信息基础类
    """
    id: int
    account_uuid: str
    username: str
    password: str
    platform: str = None
    status: int = 1
    last_login_time: str = None
    proxy_ip: str
    login_time: str
    host_name: str
    agent_uuid: str
    login_type: str = None
    user_data_dir: str = None
    cookies: str = None
    is_del: int = 0

    def to_json(self) -> str:
        return json.dumps(self.model_dump(), ensure_ascii=False, default=str)


if __name__ == '__main__':
    account = AccountModel(
        id=1,
        username="test",
        password="test",
        platform="bilibili",
        proxy="127.0.0.1:8888",
        login_time="2022-01-01 00:00:00",
        host_name="test",
        agent_uuid="test",
        login_type="qrcode"
    )
    print(account.to_json())

    for key, value in account.model_dump().items():
        print(key, value)

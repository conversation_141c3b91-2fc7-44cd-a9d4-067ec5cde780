# -*- coding: utf-8 -*-
"""
爬取服务 - 协调 API 和爬虫核心的业务逻辑层
"""
import asyncio
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum

from core.account_manager import AccountManager
from core.browser_manager import BrowserManager
from core.proxy_manager import ProxyManager
from core.crawler_worker import CrawlerWorkerManager, CrawlerWorker
from spiders.base_spider import BaseSpider, CrawlTask, CrawlType, CrawlResult
from spiders.douyin import DouyinSpider
from spiders.xiaohongshu import XiaohongshuSpider


class TaskStatus(Enum):
    """任务状态枚举"""
    CREATED = "created"         # 已创建
    RUNNING = "running"         # 运行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失败
    CANCELLED = "cancelled"     # 已取消


class CrawlService:
    """
    爬取服务 - 协调 API 和爬虫核心
    """
    
    def __init__(self):
        # 初始化核心管理器
        self.account_manager = AccountManager()
        self.browser_manager = BrowserManager()
        self.proxy_manager = ProxyManager()
        self.worker_manager = CrawlerWorkerManager(
            self.account_manager,
            self.browser_manager,
            self.proxy_manager
        )
        
        # 爬虫实例映射
        self.spider_classes = {
            "douyin": DouyinSpider,
            "dy": DouyinSpider,
            "xiaohongshu": XiaohongshuSpider,
            "xhs": XiaohongshuSpider,
            # 可以继续添加其他平台
        }
        
        # 任务存储（实际项目中应该使用数据库）
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self._initialized = False
        
    async def initialize(self):
        """初始化服务"""
        if self._initialized:
            return
            
        await self.account_manager.initialize()
        await self.browser_manager.initialize()
        await self.proxy_manager.initialize()
        
        self._initialized = True
        
    async def create_crawl_task(self,
                              platform: str,
                              crawl_type: str,
                              keywords: Optional[str] = None,
                              user_id: Optional[str] = None,
                              note_id: Optional[str] = None,
                              limit: int = 10,
                              sort_type: Optional[str] = None,
                              enable_login: bool = False,
                              use_proxy: bool = False) -> str:
        """创建爬取任务"""
        await self.initialize()
        
        task_id = str(uuid.uuid4())
        
        # 创建任务记录
        task_record = {
            "task_id": task_id,
            "platform": platform,
            "crawl_type": crawl_type,
            "keywords": keywords,
            "user_id": user_id,
            "note_id": note_id,
            "limit": limit,
            "sort_type": sort_type,
            "enable_login": enable_login,
            "use_proxy": use_proxy,
            "status": TaskStatus.CREATED.value,
            "created_at": datetime.now().isoformat(),
            "result": None,
            "error_message": None
        }
        
        self.tasks[task_id] = task_record
        return task_id
        
    async def execute_crawl_task(self, task_id: str):
        """执行爬取任务"""
        task_record = self.tasks.get(task_id)
        if not task_record:
            return
            
        try:
            # 更新任务状态
            task_record["status"] = TaskStatus.RUNNING.value
            task_record["started_at"] = datetime.now().isoformat()
            
            # 创建爬虫工作单元
            worker = await self.worker_manager.create_worker(
                platform=task_record["platform"],
                use_account=task_record["enable_login"],
                use_proxy=task_record["use_proxy"]
            )
            
            if not worker or worker.status.value == "error":
                task_record["status"] = TaskStatus.FAILED.value
                task_record["error_message"] = worker.error_message if worker else "创建工作单元失败"
                return
                
            # 创建爬虫实例
            spider_class = self.spider_classes.get(task_record["platform"])
            if not spider_class:
                task_record["status"] = TaskStatus.FAILED.value
                task_record["error_message"] = f"不支持的平台: {task_record['platform']}"
                await self.worker_manager.destroy_worker(worker.worker_id)
                return
                
            spider = spider_class()
            await spider.set_worker(worker)
            
            # 启动工作单元
            await self.worker_manager.start_worker(worker.worker_id)
            
            # 如果需要登录，执行登录
            if task_record["enable_login"]:
                login_success = await spider.login()
                if not login_success:
                    task_record["status"] = TaskStatus.FAILED.value
                    task_record["error_message"] = "登录失败"
                    await self.worker_manager.destroy_worker(worker.worker_id)
                    return
                    
            # 创建爬取任务
            crawl_task = CrawlTask(
                task_id=task_id,
                platform=task_record["platform"],
                crawl_type=CrawlType(task_record["crawl_type"]),
                keywords=task_record["keywords"],
                user_id=task_record["user_id"],
                note_id=task_record["note_id"],
                limit=task_record["limit"],
                sort_type=task_record["sort_type"]
            )
            
            # 执行爬取
            result = await spider.execute_task(crawl_task)
            
            # 更新任务结果
            if result.success:
                task_record["status"] = TaskStatus.COMPLETED.value
                task_record["result"] = {
                    "total_count": result.total_count,
                    "data": result.data
                }
            else:
                task_record["status"] = TaskStatus.FAILED.value
                task_record["error_message"] = result.error_message
                
            task_record["completed_at"] = datetime.now().isoformat()
            
            # 停止工作单元
            await self.worker_manager.stop_worker(worker.worker_id, success=result.success)
            
        except Exception as e:
            task_record["status"] = TaskStatus.FAILED.value
            task_record["error_message"] = f"执行任务异常: {str(e)}"
            task_record["completed_at"] = datetime.now().isoformat()
            
    async def get_crawl_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取爬取结果"""
        task_record = self.tasks.get(task_id)
        if not task_record:
            return None
            
        return {
            "task_id": task_id,
            "status": task_record["status"],
            "platform": task_record["platform"],
            "total_count": task_record.get("result", {}).get("total_count", 0),
            "data": task_record.get("result", {}).get("data", []),
            "error_message": task_record.get("error_message")
        }
        
    async def get_task_status(self, task_id: str) -> Optional[str]:
        """获取任务状态"""
        task_record = self.tasks.get(task_id)
        return task_record["status"] if task_record else None
        
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task_record = self.tasks.get(task_id)
        if not task_record:
            return False
            
        if task_record["status"] in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.CANCELLED.value]:
            return False
            
        task_record["status"] = TaskStatus.CANCELLED.value
        task_record["cancelled_at"] = datetime.now().isoformat()
        return True
        
    async def list_tasks(self,
                        status: Optional[str] = None,
                        platform: Optional[str] = None,
                        limit: int = 20,
                        offset: int = 0) -> List[Dict[str, Any]]:
        """获取任务列表"""
        tasks = list(self.tasks.values())
        
        # 过滤
        if status:
            tasks = [t for t in tasks if t["status"] == status]
        if platform:
            tasks = [t for t in tasks if t["platform"] == platform]
            
        # 排序（按创建时间倒序）
        tasks.sort(key=lambda x: x["created_at"], reverse=True)
        
        # 分页
        return tasks[offset:offset + limit]
        
    async def get_crawler_status(self) -> Dict[str, Any]:
        """获取爬虫状态"""
        return {
            "accounts": await self.account_manager.get_all_accounts_status(),
            "browsers": await self.browser_manager.get_all_browser_status(),
            "proxies": self.proxy_manager.get_proxy_status(),
            "workers": await self.worker_manager.get_worker_status()
        }
        
    async def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计"""
        total_tasks = len(self.tasks)
        status_count = {}
        platform_count = {}
        
        for task in self.tasks.values():
            status = task["status"]
            platform = task["platform"]
            
            status_count[status] = status_count.get(status, 0) + 1
            platform_count[platform] = platform_count.get(platform, 0) + 1
            
        return {
            "total_tasks": total_tasks,
            "status_distribution": status_count,
            "platform_distribution": platform_count
        }

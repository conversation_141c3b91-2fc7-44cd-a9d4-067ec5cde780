# -*- coding: utf-8 -*-
# @Desc    : 青果代理HTTP实现，基于 crawlers/proxy/qing_guo.py 中的试用方法
import os
import time
from typing import Dict, List

import httpx
from pydantic import BaseModel, Field

from crawlers.proxy import IpCache, IpInfoModel, ProxyProvider
from crawlers.proxy.types import ProviderNameEnum
from tools import utils


class QingguoProxyModel(BaseModel):
    server: str = Field("服务器地址")
    expire_ts: int = Field("过期时间")


def parse_qingguo_proxy(proxy_info: Dict) -> QingguoProxyModel:
    """
    解析青果代理的IP信息
    Args:
        proxy_info: 青果代理返回的数据

    Returns:
        QingguoProxyModel

    """
    if not proxy_info.get('data') or len(proxy_info.get('data', [])) == 0:
        raise Exception("not invalid qingguo proxy info")

    server = proxy_info.get('data', [])[0].get("server")
    if not server:
        raise Exception("not found server in qingguo proxy info")

    # 青果代理没有明确的过期时间，设置一个默认的过期时间（1小时后）
    expire_ts = int(time.time()) + 3600

    return QingguoProxyModel(
        server=server,
        expire_ts=expire_ts
    )


class QingGuoProxy(ProxyProvider):
    def __init__(self, auth_key: str, auth_password: str):
        """
        青果代理初始化
        Args:
            auth_key: 认证密钥
            auth_password: 认证密码
        """
        self.auth_key = auth_key
        self.auth_password = auth_password
        self.base_url = "https://share.proxy.qg.net"
        self.ip_cache = IpCache()
        self.proxy_brand_name = ProviderNameEnum.QING_GUO_PROVIDER.value

    async def get_proxies(self, num: int) -> List[IpInfoModel]:
        """
        青果代理实现
        Args:
            num: 需要获取的代理数量

        Returns:
            List[IpInfoModel]

        """
        # 优先从缓存中拿 IP
        ip_cache_list = self.ip_cache.load_all_ip(proxy_brand_name=self.proxy_brand_name)
        if len(ip_cache_list) >= num:
            return ip_cache_list[:num]

        # 如果缓存中的数量不够，从IP代理商获取补上，再存入缓存中
        need_get_count = num - len(ip_cache_list)
        
        ip_infos: List[IpInfoModel] = []
        
        # 青果代理每次只能获取一个IP，所以需要循环获取
        for _ in range(need_get_count):
            try:
                proxy_info = await self._apply_new_ip()
                if proxy_info:
                    ip_infos.append(proxy_info)
            except Exception as e:
                utils.logger.error(f"[QingGuoProxy.get_proxies] get proxy error: {e}")
                break

        return ip_cache_list + ip_infos

    async def _apply_new_ip(self) -> IpInfoModel:
        """
        申请新的IP
        Returns:
            IpInfoModel
        """
        uri = "/get"
        params = {
            "key": self.auth_key,
            "distinct": True
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(self.base_url + uri, params=params)

            if response.status_code != 200:
                utils.logger.error(f"[QingGuoProxy._apply_new_ip] status code not 200 and response.text:{response.text}")
                raise Exception("get ip error from qingguo proxy provider and status code not 200 ...")

            ip_response: Dict = response.json()
            if ip_response.get("code") != "SUCCESS":
                utils.logger.error(f"[QingGuoProxy._apply_new_ip] code not SUCCESS and msg:{ip_response}")
                raise Exception("get ip error from qingguo proxy provider and code not SUCCESS ...")

            proxy_model = parse_qingguo_proxy(ip_response)
            
            # 解析服务器地址，格式通常是 ip:port
            server_parts = proxy_model.server.split(":")
            if len(server_parts) != 2:
                raise Exception(f"invalid server format: {proxy_model.server}")
            
            ip = server_parts[0]
            port = int(server_parts[1])

            ip_info_model = IpInfoModel(
                ip=ip,
                port=port,
                user=self.auth_key,
                password=self.auth_password,
                expired_time_ts=proxy_model.expire_ts,
            )
            
            # 缓存IP信息
            ip_key = f"{self.proxy_brand_name}_{ip_info_model.ip}_{ip_info_model.port}"
            self.ip_cache.set_ip(ip_key, ip_info_model.model_dump_json(), ex=ip_info_model.expired_time_ts)
            
            return ip_info_model


def new_qing_guo_proxy() -> QingGuoProxy:
    """
    构造青果代理HTTP实例
    Returns:
        QingGuoProxy
    """
    return QingGuoProxy(
        auth_key=os.getenv("qg_auth_key", "你的青果代理认证密钥"),
        auth_password=os.getenv("qg_auth_password", "你的青果代理认证密码"),
    )

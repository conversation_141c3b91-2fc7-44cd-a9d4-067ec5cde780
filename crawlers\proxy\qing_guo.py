# -*- encoding:utf-8 -*-
from typing import Dict

import requests


class QIngGuo:
    base_url = "https://share.proxy.qg.net"

    def __init__(self, auth_key: str, auth_password: str):
        self.auth_key = auth_key
        self.auth_password = auth_password

    def pong_proxy(self, proxy_addr):
        targetURL = "https://test.ipw.cn"

        proxyUrl = f"http://{self.auth_key}:{self.auth_password}@{proxy_addr}"

        proxies = {
            "http": proxyUrl,
            "https": proxyUrl,
        }
        resp = requests.get(targetURL, proxies=proxies)
        print(resp.text)

    def apply_new_ip(self, distinct: bool) -> Dict:
        uri = "/get"

        param = {
            "key": self.auth_key,
            "distinct": distinct
        }
        resp = requests.get(url=self.base_url + uri, params=param)
        print(resp.text)

        return resp.json()

    def query_proxy(self):
        new_ip = self.apply_new_ip(distinct=True)

        if new_ip.get('code') == "SUCCESS":
            channel_server = new_ip.get('data', {})[0]["server"]

            return f"http://{self.auth_key}:{self.auth_password}@{channel_server}"
        return None


if __name__ == '__main__':
    qingguoProxy = QIngGuo(auth_key="CDBD5AC2", auth_password="C1C2977F6BC4")

    # ip_info = qingguoProxy.apply_new_ip(distinct=True)
    if not qingguoProxy.query_proxy():
        print("获取代理失败")


    # if ip_info.get('code') == "SUCCESS":
    #     print(ip_info)
    #     server = ip_info.get('data', {})[0]["server"]
    #     qingguoProxy.pong_proxy(proxy_addr=server)

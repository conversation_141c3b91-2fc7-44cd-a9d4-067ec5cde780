# -*- coding: utf-8 -*-
"""
This module contains the AccountLogin class, which is responsible for handling the login process for the account manager.
"""

import json
import os
from typing import Dict, Optional

from playwright.async_api import BrowserContext, Page

from crawlers.spiders.douyin.douyin_search import <PERSON>uyinSearch
from crawlers.spiders.xhs.xhs_search import XiaoHongShuSearch
from tools import utils
from config import base_config as config

class AccountManager:
    """
    The AccountLogin class handles the login process for the account manager
    持久化账号数据,管理平台账号的状态和切换
    """

    context_page: Page
    browser_context: BrowserContext
    platform_instance: Optional[XiaoHongShuSearch | DouyinSearch] = None

    def __init__(self, account_dir: str = None):
        """
        初始化账号管理器
        Args:
            account_dir: 账号数据存储目录,默认为当前目录下的 account_data
        """
        self.account_dir = account_dir or os.path.join(
            os.path.dirname(__file__), "account_data"
        )
        if not os.path.exists(self.account_dir):
            os.makedirs(self.account_dir)

        # 平台账号数据文件
        self.account_file = os.path.join(self.account_dir, "accounts.json")
        # 初始化账号数据
        self.accounts: Dict[str, Dict] = self._load_accounts()

    def _load_accounts(self) -> Dict:
        """加载账号数据"""
        if os.path.exists(self.account_file):
            with open(self.account_file, "r", encoding="utf-8") as f:
                return json.load(f)
        return {}

    def _save_accounts(self):
        """保存账号数据"""
        with open(self.account_file, "w", encoding="utf-8") as f:
            json.dump(self.accounts, f, indent=2, ensure_ascii=False)

    async def start_login(self, platform: str, username: str, user_data: str):
        """
        开始登陆，初始化账号池，用于登陆小红书抖音账号并保存在本地，同时初始化账号状态并保存在本地
        Args:
            platform: 平台名称 (xhs/douyin)
            username: 用户名
            user_data: 用户数据目录(存储浏览器登录状态)

        Returns:
            None
        """
        # 初始化平台数据结构
        if platform not in self.accounts:
            self.accounts[platform] = {}

        # 添加账号信息
        self.accounts[platform][username] = {
            "username": username,
            "user_data": user_data,
            "status": 0,  # 0表示有效
        }

        self._save_accounts()

        # 初始化平台实例
        if platform == "xhs":
            self.platform_instance = XiaoHongShuSearch(
                headless=False,
                use_local_chrome=True,
                user_data_dir=user_data,
                chrome_path=config.LOCAL_CHROME_PATH
            )
        elif platform == "douyin":
            self.platform_instance = DouyinSearch(
                headless=False,
                use_local_chrome=True,
                user_data_dir=user_data,
                chrome_path=config.LOCAL_CHROME_PATH
            )

        # 初始化平台
        if self.platform_instance:
            await self.platform_instance.initialize()

        await self.platform_instance.close()
        utils.logger.info("[main_loop] 小红书浏览器已关闭")
    def apply_account(self, platform: str) -> Optional[Dict]:
        """
        其他接口初始化时,申请平台账号
        Args:
            platform: 平台名称 (xhs/douyin)

        Returns:
            Dict: 返回一个可用的账号信息,如果没有可用账号则返回None
        """
        if platform not in self.accounts:
            return None

        # 查找状态为0(有效)的账号
        for username, account in self.accounts[platform].items():
            if account["status"] == 0:
                return account

        return None

    def update_account_status(self, platform: str, username: str, status: int):
        """
        更新持久化在本地的账号状态 status = 1 为失效， 0 为有效
        Args:
            platform: 平台名称
            username: 用户名
            status: 账号状态(0:有效,1:失效)

        Returns:
            None
        """
        if platform in self.accounts and username in self.accounts[platform]:
            self.accounts[platform][username]["status"] = status
            self._save_accounts()

    async def change_online_account(self, platform: str, username: str):
        """
        更换运行账号,将当前账号标记为失效并申请新账号
        Args:
            platform: 平台名称
            username: 当前使用的用户名

        Returns:
            Dict: 返回新的可用账号信息,如无可用账号则返回None
        """
        # 将当前账号标记为失效
        self.update_account_status(platform=platform, username=username, status=1)

        # 关闭当前实例
        if self.platform_instance:
            await self.platform_instance.close()
            self.platform_instance = None

        # 获取新的可用账号
        new_account = self.apply_account(platform=platform)
        if new_account:
            if platform == "xhs":
                self.platform_instance = XiaoHongShuSearch(
                    headless=True,
                    use_local_chrome=True,
                    user_data_dir=new_account["user_data"],
                )
            elif platform == "douyin":
                self.platform_instance = DouyinSearch(
                    headless=True,
                    use_local_chrome=True,
                    user_data_dir=new_account["user_data"],
                )

            if self.platform_instance:
                await self.platform_instance.initialize()

        return new_account

    async def close(self):
        """关闭并清理资源"""
        if self.platform_instance:
            await self.platform_instance.close()
            self.platform_instance = None



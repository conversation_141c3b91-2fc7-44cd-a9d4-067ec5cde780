# -*- coding: utf-8 -*-
"""
账号管理器 - 负责账号的加载、状态管理和分配
"""
import asyncio
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path

from common.crawler_platform import CrawlerPlatform


class AccountStatus(Enum):
    """账号状态枚举"""
    AVAILABLE = "available"      # 可用
    IN_USE = "in_use"           # 使用中
    BANNED = "banned"           # 被封禁
    EXPIRED = "expired"         # 已过期
    ERROR = "error"             # 错误状态


@dataclass
class Account:
    """账号数据模型"""
    platform: str
    user_id: str
    username: str
    cookies: Dict[str, Any]
    status: AccountStatus = AccountStatus.AVAILABLE
    last_used: Optional[str] = None
    error_count: int = 0
    max_error_count: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "platform": self.platform,
            "user_id": self.user_id,
            "username": self.username,
            "cookies": self.cookies,
            "status": self.status.value,
            "last_used": self.last_used,
            "error_count": self.error_count,
            "max_error_count": self.max_error_count
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Account":
        """从字典创建账号对象"""
        return cls(
            platform=data["platform"],
            user_id=data["user_id"],
            username=data["username"],
            cookies=data["cookies"],
            status=AccountStatus(data.get("status", "available")),
            last_used=data.get("last_used"),
            error_count=data.get("error_count", 0),
            max_error_count=data.get("max_error_count", 3)
        )


class AccountManager:
    """
    账号管理器 - 负责账号的加载、状态管理和分配
    """
    
    def __init__(self, account_store_path: str = "crawlers/account/account_store"):
        self.account_store_path = Path(account_store_path)
        self.accounts: Dict[str, List[Account]] = {}  # platform -> accounts
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """初始化账号管理器，加载所有平台的账号"""
        await self.load_all_accounts()
        
    async def load_all_accounts(self):
        """加载所有平台的账号"""
        async with self._lock:
            for platform in CrawlerPlatform:
                await self._load_platform_accounts(platform.value)
                
    async def _load_platform_accounts(self, platform: str):
        """加载指定平台的账号"""
        platform_dir = self.account_store_path / platform
        if not platform_dir.exists():
            self.accounts[platform] = []
            return
            
        accounts = []
        for account_file in platform_dir.glob("*.json"):
            try:
                with open(account_file, 'r', encoding='utf-8') as f:
                    account_data = json.load(f)
                    account = Account.from_dict(account_data)
                    accounts.append(account)
            except Exception as e:
                print(f"加载账号文件失败 {account_file}: {e}")
                
        self.accounts[platform] = accounts
        print(f"已加载 {platform} 平台账号: {len(accounts)} 个")
        
    async def get_available_account(self, platform: str) -> Optional[Account]:
        """获取指定平台的可用账号"""
        async with self._lock:
            platform_accounts = self.accounts.get(platform, [])
            
            # 查找可用账号
            for account in platform_accounts:
                if account.status == AccountStatus.AVAILABLE:
                    account.status = AccountStatus.IN_USE
                    return account
                    
            return None
            
    async def release_account(self, platform: str, user_id: str, success: bool = True):
        """释放账号，更新状态"""
        async with self._lock:
            platform_accounts = self.accounts.get(platform, [])
            
            for account in platform_accounts:
                if account.user_id == user_id:
                    if success:
                        account.status = AccountStatus.AVAILABLE
                        account.error_count = 0
                    else:
                        account.error_count += 1
                        if account.error_count >= account.max_error_count:
                            account.status = AccountStatus.ERROR
                        else:
                            account.status = AccountStatus.AVAILABLE
                    break
                    
    async def mark_account_banned(self, platform: str, user_id: str):
        """标记账号为被封禁状态"""
        async with self._lock:
            platform_accounts = self.accounts.get(platform, [])
            
            for account in platform_accounts:
                if account.user_id == user_id:
                    account.status = AccountStatus.BANNED
                    break
                    
    async def get_account_status(self, platform: str) -> Dict[str, int]:
        """获取指定平台的账号状态统计"""
        async with self._lock:
            platform_accounts = self.accounts.get(platform, [])
            
            status_count = {status.value: 0 for status in AccountStatus}
            for account in platform_accounts:
                status_count[account.status.value] += 1
                
            return status_count
            
    async def get_all_accounts_status(self) -> Dict[str, Dict[str, int]]:
        """获取所有平台的账号状态统计"""
        result = {}
        for platform in self.accounts.keys():
            result[platform] = await self.get_account_status(platform)
        return result
        
    async def save_account(self, account: Account):
        """保存账号到文件"""
        platform_dir = self.account_store_path / account.platform
        platform_dir.mkdir(parents=True, exist_ok=True)
        
        account_file = platform_dir / f"{account.user_id}.json"
        with open(account_file, 'w', encoding='utf-8') as f:
            json.dump(account.to_dict(), f, ensure_ascii=False, indent=2)
            
    async def add_account(self, account: Account):
        """添加新账号"""
        async with self._lock:
            if account.platform not in self.accounts:
                self.accounts[account.platform] = []
                
            # 检查是否已存在
            existing = any(acc.user_id == account.user_id 
                          for acc in self.accounts[account.platform])
            if not existing:
                self.accounts[account.platform].append(account)
                await self.save_account(account)
                
    async def remove_account(self, platform: str, user_id: str):
        """移除账号"""
        async with self._lock:
            platform_accounts = self.accounts.get(platform, [])
            self.accounts[platform] = [
                acc for acc in platform_accounts if acc.user_id != user_id
            ]
            
            # 删除文件
            account_file = self.account_store_path / platform / f"{user_id}.json"
            if account_file.exists():
                account_file.unlink()
                
    def get_account_count(self, platform: str) -> int:
        """获取指定平台的账号总数"""
        return len(self.accounts.get(platform, []))

# -*- coding: utf-8 -*-
"""
代理管理器 - 负责 IP 代理池的管理和调度
"""
import asyncio
import random
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import aiohttp
import time

from crawlers.proxy.proxy_ip_pool import ProxyIpPool


class ProxyStatus(Enum):
    """代理状态枚举"""
    AVAILABLE = "available"     # 可用
    IN_USE = "in_use"          # 使用中
    FAILED = "failed"          # 失败
    BANNED = "banned"          # 被封禁
    TIMEOUT = "timeout"        # 超时


@dataclass
class ProxyInfo:
    """代理信息数据模型"""
    ip: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = "http"
    status: ProxyStatus = ProxyStatus.AVAILABLE
    last_used: Optional[float] = None
    success_count: int = 0
    fail_count: int = 0
    response_time: Optional[float] = None
    
    @property
    def proxy_url(self) -> str:
        """获取代理URL"""
        if self.username and self.password:
            return f"{self.protocol}://{self.username}:{self.password}@{self.ip}:{self.port}"
        return f"{self.protocol}://{self.ip}:{self.port}"
    
    @property
    def success_rate(self) -> float:
        """获取成功率"""
        total = self.success_count + self.fail_count
        return self.success_count / total if total > 0 else 0.0
    
    def to_playwright_proxy(self) -> Dict[str, Any]:
        """转换为 Playwright 代理格式"""
        proxy_config = {
            "server": f"{self.protocol}://{self.ip}:{self.port}"
        }
        if self.username and self.password:
            proxy_config["username"] = self.username
            proxy_config["password"] = self.password
        return proxy_config


class ProxyManager:
    """
    代理管理器 - 负责 IP 代理池的管理和调度
    """
    
    def __init__(self, 
                 max_fail_count: int = 3,
                 health_check_interval: int = 300,
                 proxy_timeout: int = 10):
        self.max_fail_count = max_fail_count
        self.health_check_interval = health_check_interval
        self.proxy_timeout = proxy_timeout
        
        self.proxies: List[ProxyInfo] = []
        self.proxy_ip_pool = ProxyIpPool()
        self._lock = asyncio.Lock()
        self._health_check_task = None
        
    async def initialize(self):
        """初始化代理管理器"""
        await self.load_proxies()
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
    async def load_proxies(self):
        """加载代理列表"""
        async with self._lock:
            try:
                # 从代理池获取代理列表
                proxy_list = await self.proxy_ip_pool.get_proxy_list()
                
                self.proxies = []
                for proxy_data in proxy_list:
                    proxy_info = ProxyInfo(
                        ip=proxy_data.get("ip"),
                        port=proxy_data.get("port"),
                        username=proxy_data.get("username"),
                        password=proxy_data.get("password"),
                        protocol=proxy_data.get("protocol", "http")
                    )
                    self.proxies.append(proxy_info)
                    
                print(f"已加载代理: {len(self.proxies)} 个")
                
            except Exception as e:
                print(f"加载代理失败: {e}")
                
    async def get_proxy(self, exclude_failed: bool = True) -> Optional[ProxyInfo]:
        """获取可用代理"""
        async with self._lock:
            available_proxies = []
            
            for proxy in self.proxies:
                if proxy.status == ProxyStatus.AVAILABLE:
                    if exclude_failed and proxy.fail_count >= self.max_fail_count:
                        continue
                    available_proxies.append(proxy)
                    
            if not available_proxies:
                return None
                
            # 根据成功率和响应时间选择最佳代理
            best_proxy = self._select_best_proxy(available_proxies)
            best_proxy.status = ProxyStatus.IN_USE
            best_proxy.last_used = time.time()
            
            return best_proxy
            
    def _select_best_proxy(self, proxies: List[ProxyInfo]) -> ProxyInfo:
        """选择最佳代理"""
        # 优先选择成功率高且响应时间短的代理
        def proxy_score(proxy: ProxyInfo) -> float:
            success_rate = proxy.success_rate
            response_time = proxy.response_time or 999.0
            
            # 成功率权重 0.7，响应时间权重 0.3
            score = success_rate * 0.7 + (1.0 / (response_time + 1)) * 0.3
            return score
            
        return max(proxies, key=proxy_score)
        
    async def release_proxy(self, proxy: ProxyInfo, success: bool = True):
        """释放代理"""
        async with self._lock:
            proxy.status = ProxyStatus.AVAILABLE
            
            if success:
                proxy.success_count += 1
            else:
                proxy.fail_count += 1
                
                # 如果失败次数过多，标记为失败状态
                if proxy.fail_count >= self.max_fail_count:
                    proxy.status = ProxyStatus.FAILED
                    
    async def mark_proxy_banned(self, proxy: ProxyInfo):
        """标记代理为被封禁状态"""
        async with self._lock:
            proxy.status = ProxyStatus.BANNED
            
    async def test_proxy(self, proxy: ProxyInfo, test_url: str = "http://httpbin.org/ip") -> bool:
        """测试代理可用性"""
        try:
            start_time = time.time()
            
            connector = aiohttp.TCPConnector()
            timeout = aiohttp.ClientTimeout(total=self.proxy_timeout)
            
            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            ) as session:
                async with session.get(
                    test_url,
                    proxy=proxy.proxy_url
                ) as response:
                    if response.status == 200:
                        proxy.response_time = time.time() - start_time
                        return True
                        
        except Exception as e:
            print(f"代理测试失败 {proxy.ip}:{proxy.port} - {e}")
            
        return False
        
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"健康检查异常: {e}")
                
    async def _health_check(self):
        """执行健康检查"""
        async with self._lock:
            print("开始代理健康检查...")
            
            # 测试失败状态的代理
            failed_proxies = [p for p in self.proxies if p.status == ProxyStatus.FAILED]
            
            for proxy in failed_proxies:
                if await self.test_proxy(proxy):
                    proxy.status = ProxyStatus.AVAILABLE
                    proxy.fail_count = 0
                    print(f"代理恢复: {proxy.ip}:{proxy.port}")
                    
            print(f"健康检查完成，可用代理: {self.get_available_count()}")
            
    def get_available_count(self) -> int:
        """获取可用代理数量"""
        return len([p for p in self.proxies if p.status == ProxyStatus.AVAILABLE])
        
    def get_proxy_status(self) -> Dict[str, int]:
        """获取代理状态统计"""
        status_count = {status.value: 0 for status in ProxyStatus}
        for proxy in self.proxies:
            status_count[proxy.status.value] += 1
        return status_count
        
    async def refresh_proxy_pool(self):
        """刷新代理池"""
        await self.load_proxies()
        
    async def cleanup(self):
        """清理资源"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

# -*- coding: utf-8 -*-
"""
浏览器管理器 - 负责 Playwright 实例池管理和指纹切换
"""
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontext, Page

from common.crawler_platform import CrawlerPlatform
from crawlers.browser.virtual_browser_helper import VirtualBrowserHelper


class BrowserStatus(Enum):
    """浏览器状态枚举"""
    IDLE = "idle"           # 空闲
    BUSY = "busy"           # 忙碌
    ERROR = "error"         # 错误
    CLOSED = "closed"       # 已关闭


@dataclass
class BrowserInstance:
    """浏览器实例数据模型"""
    browser_id: str
    platform: str
    browser: Optional[Browser] = None
    context: Optional[BrowserContext] = None
    page: Optional[Page] = None
    status: BrowserStatus = BrowserStatus.IDLE
    debug_port: Optional[str] = None
    user_agent: Optional[str] = None
    proxy: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None
    last_used: Optional[str] = None


class BrowserManager:
    """
    浏览器管理器 - 负责 Playwright 实例池管理和指纹切换
    """
    
    def __init__(self, 
                 max_browsers_per_platform: int = 5,
                 virtual_browser_api_key: Optional[str] = None,
                 use_virtual_browser: bool = False):
        self.max_browsers_per_platform = max_browsers_per_platform
        self.use_virtual_browser = use_virtual_browser
        self.virtual_browser_helper = None
        
        if use_virtual_browser and virtual_browser_api_key:
            self.virtual_browser_helper = VirtualBrowserHelper(virtual_browser_api_key)
            
        self.browsers: Dict[str, List[BrowserInstance]] = {}  # platform -> browsers
        self.playwright = None
        self._lock = asyncio.Lock()
        
    async def initialize(self):
        """初始化浏览器管理器"""
        self.playwright = await async_playwright().start()
        
        # 如果使用虚拟浏览器，初始化分组
        if self.virtual_browser_helper:
            platforms = [CrawlerPlatform.X_H_S, CrawlerPlatform.D_Y, 
                        CrawlerPlatform.K_S, CrawlerPlatform.BILI]
            self.virtual_browser_helper.set_up_group(platforms)
            
        # 初始化各平台的浏览器池
        for platform in CrawlerPlatform:
            self.browsers[platform.value] = []
            
    async def get_browser_instance(self, 
                                 platform: str, 
                                 proxy: Optional[Dict[str, Any]] = None,
                                 user_agent: Optional[str] = None) -> Optional[BrowserInstance]:
        """获取浏览器实例"""
        async with self._lock:
            # 查找空闲的浏览器实例
            platform_browsers = self.browsers.get(platform, [])
            
            for browser_instance in platform_browsers:
                if browser_instance.status == BrowserStatus.IDLE:
                    browser_instance.status = BrowserStatus.BUSY
                    return browser_instance
                    
            # 如果没有空闲实例且未达到最大数量，创建新实例
            if len(platform_browsers) < self.max_browsers_per_platform:
                browser_instance = await self._create_browser_instance(
                    platform, proxy, user_agent
                )
                if browser_instance:
                    self.browsers[platform].append(browser_instance)
                    return browser_instance
                    
            return None
            
    async def _create_browser_instance(self, 
                                     platform: str,
                                     proxy: Optional[Dict[str, Any]] = None,
                                     user_agent: Optional[str] = None) -> Optional[BrowserInstance]:
        """创建新的浏览器实例"""
        try:
            browser_id = f"{platform}_{len(self.browsers.get(platform, []))}"
            
            if self.use_virtual_browser and self.virtual_browser_helper:
                # 使用虚拟浏览器
                debug_port = await self.virtual_browser_helper.apply_browser(
                    launch_new=False,
                    crawler_group=CrawlerPlatform(platform)
                )
                
                if debug_port:
                    browser = await self.playwright.chromium.connect_over_cdp(
                        f"http://localhost:{debug_port}"
                    )
                    context = browser.contexts[0] if browser.contexts else await browser.new_context()
                    page = await context.new_page()
                    
                    return BrowserInstance(
                        browser_id=browser_id,
                        platform=platform,
                        browser=browser,
                        context=context,
                        page=page,
                        status=BrowserStatus.BUSY,
                        debug_port=debug_port,
                        user_agent=user_agent,
                        proxy=proxy
                    )
            else:
                # 使用本地浏览器
                launch_options = {
                    "headless": True,
                    "args": [
                        "--no-sandbox",
                        "--disable-setuid-sandbox",
                        "--disable-dev-shm-usage",
                        "--disable-gpu",
                        "--no-first-run",
                        "--no-default-browser-check",
                        "--disable-default-apps"
                    ]
                }
                
                if proxy:
                    launch_options["proxy"] = proxy
                    
                browser = await self.playwright.chromium.launch(**launch_options)
                
                context_options = {}
                if user_agent:
                    context_options["user_agent"] = user_agent
                    
                context = await browser.new_context(**context_options)
                page = await context.new_page()
                
                return BrowserInstance(
                    browser_id=browser_id,
                    platform=platform,
                    browser=browser,
                    context=context,
                    page=page,
                    status=BrowserStatus.BUSY,
                    user_agent=user_agent,
                    proxy=proxy
                )
                
        except Exception as e:
            print(f"创建浏览器实例失败: {e}")
            return None
            
    async def release_browser_instance(self, browser_instance: BrowserInstance):
        """释放浏览器实例"""
        async with self._lock:
            browser_instance.status = BrowserStatus.IDLE
            
    async def close_browser_instance(self, browser_instance: BrowserInstance):
        """关闭浏览器实例"""
        async with self._lock:
            try:
                if browser_instance.page:
                    await browser_instance.page.close()
                if browser_instance.context:
                    await browser_instance.context.close()
                if browser_instance.browser:
                    await browser_instance.browser.close()
                    
                browser_instance.status = BrowserStatus.CLOSED
                
                # 从池中移除
                platform_browsers = self.browsers.get(browser_instance.platform, [])
                self.browsers[browser_instance.platform] = [
                    b for b in platform_browsers if b.browser_id != browser_instance.browser_id
                ]
                
            except Exception as e:
                print(f"关闭浏览器实例失败: {e}")
                
    async def get_browser_status(self, platform: str) -> Dict[str, int]:
        """获取指定平台的浏览器状态统计"""
        async with self._lock:
            platform_browsers = self.browsers.get(platform, [])
            
            status_count = {status.value: 0 for status in BrowserStatus}
            for browser_instance in platform_browsers:
                status_count[browser_instance.status.value] += 1
                
            return status_count
            
    async def get_all_browser_status(self) -> Dict[str, Dict[str, int]]:
        """获取所有平台的浏览器状态统计"""
        result = {}
        for platform in self.browsers.keys():
            result[platform] = await self.get_browser_status(platform)
        return result
        
    async def cleanup_idle_browsers(self, max_idle_time: int = 300):
        """清理长时间空闲的浏览器实例"""
        async with self._lock:
            import time
            current_time = time.time()
            
            for platform, browsers in self.browsers.items():
                idle_browsers = [
                    b for b in browsers 
                    if (b.status == BrowserStatus.IDLE and 
                        b.last_used and 
                        current_time - float(b.last_used) > max_idle_time)
                ]
                
                for browser in idle_browsers:
                    await self.close_browser_instance(browser)
                    
    async def close_all_browsers(self):
        """关闭所有浏览器实例"""
        async with self._lock:
            for platform, browsers in self.browsers.items():
                for browser in browsers[:]:  # 创建副本以避免修改列表时的问题
                    await self.close_browser_instance(browser)
                    
        if self.playwright:
            await self.playwright.stop()

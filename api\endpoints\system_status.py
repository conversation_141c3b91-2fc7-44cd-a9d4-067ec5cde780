# -*- coding: utf-8 -*-
"""
系统状态 API 端点
"""
import psutil
import asyncio
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends

from services.crawl_service import CrawlService


router = APIRouter()


def get_crawl_service() -> CrawlService:
    """依赖注入：获取爬取服务实例"""
    return CrawlService()


@router.get("/status")
async def get_system_status():
    """
    获取系统状态信息
    """
    try:
        # 获取系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 获取网络连接数
        connections = len(psutil.net_connections())
        
        return {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "percent": memory.percent,
                    "used": memory.used
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                },
                "network_connections": connections
            },
            "service": {
                "name": "MediaCrawler",
                "version": "1.0.0",
                "status": "running"
            }
        }
        
    except Exception as e:
        return {
            "timestamp": datetime.now().isoformat(),
            "error": f"获取系统状态失败: {str(e)}",
            "service": {
                "name": "MediaCrawler",
                "version": "1.0.0",
                "status": "error"
            }
        }


@router.get("/status/crawlers")
async def get_crawler_status(
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    获取爬虫状态信息
    """
    try:
        # 获取各平台爬虫状态
        crawler_status = await crawl_service.get_crawler_status()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "crawlers": crawler_status
        }
        
    except Exception as e:
        return {
            "timestamp": datetime.now().isoformat(),
            "error": f"获取爬虫状态失败: {str(e)}"
        }


@router.get("/status/tasks")
async def get_task_statistics(
    crawl_service: CrawlService = Depends(get_crawl_service)
):
    """
    获取任务统计信息
    """
    try:
        # 获取任务统计
        stats = await crawl_service.get_task_statistics()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "task_statistics": stats
        }
        
    except Exception as e:
        return {
            "timestamp": datetime.now().isoformat(),
            "error": f"获取任务统计失败: {str(e)}"
        }


@router.get("/status/resources")
async def get_resource_status():
    """
    获取资源状态（浏览器、代理、账号等）
    """
    try:
        # 这里可以添加获取浏览器池、代理池、账号池状态的逻辑
        # 暂时返回模拟数据
        return {
            "timestamp": datetime.now().isoformat(),
            "resources": {
                "browser_pool": {
                    "total": 10,
                    "active": 3,
                    "idle": 7
                },
                "proxy_pool": {
                    "total": 50,
                    "active": 15,
                    "available": 35
                },
                "account_pool": {
                    "total": 20,
                    "active": 8,
                    "available": 12
                }
            }
        }
        
    except Exception as e:
        return {
            "timestamp": datetime.now().isoformat(),
            "error": f"获取资源状态失败: {str(e)}"
        }


@router.post("/status/health-check")
async def health_check():
    """
    健康检查端点
    """
    try:
        # 执行基本的健康检查
        checks = {
            "database": await _check_database(),
            "cache": await _check_cache(),
            "browser": await _check_browser_service()
        }
        
        all_healthy = all(checks.values())
        
        return {
            "timestamp": datetime.now().isoformat(),
            "status": "healthy" if all_healthy else "unhealthy",
            "checks": checks
        }
        
    except Exception as e:
        return {
            "timestamp": datetime.now().isoformat(),
            "status": "error",
            "error": f"健康检查失败: {str(e)}"
        }


async def _check_database() -> bool:
    """检查数据库连接"""
    try:
        # 这里添加数据库连接检查逻辑
        # 暂时返回 True
        return True
    except:
        return False


async def _check_cache() -> bool:
    """检查缓存服务"""
    try:
        # 这里添加缓存服务检查逻辑
        # 暂时返回 True
        return True
    except:
        return False


async def _check_browser_service() -> bool:
    """检查浏览器服务"""
    try:
        # 这里添加浏览器服务检查逻辑
        # 暂时返回 True
        return True
    except:
        return False
